import { Button } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import './Page404.less';

export default function Page404() {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="page-404">
      <div className="page-404__container">
        <div className="page-404__content">
          <div className="page-404__icon">
            <div className="page-404__number">404</div>
          </div>

          <div className="page-404__text">
            <h1 className="page-404__title">页面未找到</h1>
            <p className="page-404__description">抱歉，您访问的页面不存在或已被移除。</p>

            <div className="page-404__actions">
              <Button type="primary" size="large" icon={<HomeOutlined />} onClick={handleGoHome}>
                返回首页
              </Button>
              <Button size="large" icon={<ArrowLeftOutlined />} onClick={handleGoBack}>
                返回上页
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
