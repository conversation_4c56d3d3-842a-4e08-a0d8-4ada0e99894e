import React, { useState } from 'react';
import { Drawer, Button, Table, Tag, Switch, Image, Tabs } from 'antd';
import { CloseOutlined, DownloadOutlined } from '@ant-design/icons';
import './DrawerDetailDatabase.less';
import image_1749625960270_1e8drs from '../../images/image_1749625960270_1e8drs.svg';
import image_1749625966598_ckpjxz from '../../images/image_1749625966598_ckpjxz.svg';
import image_1749625972548_1qi5fp from '../../images/image_1749625972548_1qi5fp.svg';
import image_1749625978675_jzgl86 from '../../images/image_1749625978675_jzgl86.svg';
import image_1749625984405_mnwt2g from '../../images/image_1749625984405_mnwt2g.svg';
interface DrawerComponentProps {
  /** 抽屉是否可见 */
  visible: boolean;
  /** 抽屉标题 */
  title?: string;
  /** 抽屉内容 */
  content?: React.ReactNode;
  /** 确认按钮文本 */
  okText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 抽屉宽度 */
  width?: number | string;
  /** 是否显示遮罩 */
  mask?: boolean;
  /** 点击遮罩是否关闭 */
  maskClosable?: boolean;
  /** 确认回调 */
  onOk?: () => void;
  /** 取消回调 */
  onCancel?: () => void;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 自定义抽屉组件
 * 基于 Ant Design Modal 实现的侧边抽屉效果
 */
const DrawerComponent: React.FC<DrawerComponentProps> = ({
  visible,
  title = '详情',
  content,
  okText = '确定',
  cancelText = '取消',
  width = '50%',
  mask = true,
  maskClosable = true,
  onOk,
  onCancel,
  onClose,
}) => {
  // 添加标签页状态
  const [activeTab, setActiveTab] = useState(0);

  // 表格数据
  const tableData = [
    {
      key: '1',
      organPart: '肺',
      species: '人',
      dataType: 'CT,RTSTRUCT',
      cancerType: '非小细胞肺癌',
      size: '183.04GB',
      supportData: '图像分析',
      status: '公共,完整',
      updateTime: '2016/10/19',
    },
  ];

  // 表格列定义
  const columns = [
    {
      title: '组织部位',
      dataIndex: 'organPart',
      key: 'organPart',
    },
    {
      title: '物种',
      dataIndex: 'species',
      key: 'species',
    },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      key: 'dataType',
    },
    {
      title: '癌症类型',
      dataIndex: 'cancerType',
      key: 'cancerType',
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: '支持数据',
      dataIndex: 'supportData',
      key: 'supportData',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
  ];

  // 标签页配置
  const items = [
    {
      key: '1',
      label: '总结',
      children: (
        <div className="drawer-component__detail">
          <p className="drawer-component__detail-text">
            This data collection consists of images acquired during chemoradiotherapy of 20 locally-advanced, non-small cell lung cancer
            patients. The images include four-dimensional (4D) fan beam (4D-FBCT) and 4D cone beam CT (4D-CBCT). All patients underwent
            concurrent radiochemotherapy to a total dose of 64.8-70 Gy using daily 1.8 or 2 Gy fractions.  4D-FBCT images were acquired on a
            16-slice helical CT scanner (Brilliance Big Bore, Philips Medical Systems, Andover, MA) as respiration-correlated CTs with 10
            breathing phases (0 to 90%, phase-based binning) and 3 mm slice thickness. 4D-FBCT images were acquired during simulation, prior
            to therapy, and used for therapy planning. In 14 of the 20 subjects, 4D-FBCTs were also acquired on the same scanner weekly
            during therapy. 4D-CBCT images were acquired on a commercial CBCT scanner (On-Board Imager™, Varian Medical Systems, Inc.). An
            external surrogate (Real-time Position Management, Varian Medical Systems, Inc.) was integrated into the CBCT acquisition system
            to stamp each CBCT projection with the surrogate respiratory signal through in-house software and hardware tools. Approximately
            2500 projections were acquired over a period of 8-10 minutes in half-fan mode with half bow-tie filter. The technique was 125
            kVp, 20 mA, and 20 ms in a single 360° slow gantry arc. Using the external surrogate, the CBCT projections were sorted into 10
            breathing phases (0 to 90%, phase-based binning) and reconstructed with an in-house FDK reconstruction algorithm. Audio-visual
            biofeedback was performed for all 4D-FBCT and 4D-CBCT acquisitions in all subjects. A single Radiation Oncologist delineated
            targets and organs at risk in all 4D-FBCT and a limited number of 4D-CBCT images, on all 10 phases per scan. Seven of the
            subjects had gold coils implanted as fiducial markers in or near the tumor.  The dataset is most fully described in detail in
            Balik et al.1  Seven of the subjects had gold coils implanted as fiducial markers in or near the tumor. The implantation
            procedure and details of marker location are described in detail in Roman et al.2 ReferencesThis data collection consists of
            images acquired during chemoradiotherapy of 20 locally-advanced, non-small cell lung cancer patients. The images include
            four-dimensional (4D) fan beam (4D-FBCT) and 4D cone beam CT (4D-CBCT). All patients underwent concurrent radiochemotherapy to a
            total dose of 64.8-70 Gy using daily 1.8 or 2 Gy fractions.  4D-FBCT images were acquired on a 16-slice helical CT scanner
            (Brilliance Big Bore, Philips Medical Systems, Andover, MA) as respiration-correlated CTs with 10 breathing phases (0 to 90%,
            phase-based binning) and 3 mm slice thickness. 4D-FBCT images were acquired during simulation, prior to therapy, and used for
            therapy planning. In 14 of the 20 subjects, 4D-FBCTs were also acquired on the same scanner weekly during therapy. 4D-CBCT
            images were acquired on a commercial CBCT scanner (On-Board Imager™, Varian Medical Systems, Inc.). An external surrogate
            (Real-time Position Management, Varian Medical Systems, Inc.) was integrated into the CBCT acquisition system to stamp each CBCT
            projection with the surrogate respiratory signal through in-house software and hardware tools. Approximately 2500 projections
            were acquired over a period of 8-10 minutes in half-fan mode with half bow-tie filter. The technique was 125 kVp, 20 mA, and 20
            ms in a single 360° slow gantry arc. Using the external surrogate, the CBCT projections were sorted into 10 breathing phases (0
            to 90%, phase-based binning) and reconstructed with an in-house FDK reconstruction algorithm. Audio-visual biofeedback was
            performed for all 4D-FBCT and 4D-CBCT acquisitions in all subjects. A single Radiation Oncologist delineated targets and organs
            at risk in all 4D-FBCT and a limited number of 4D-CBCT images, on all 10 phases per scan. Seven of the subjects had gold coils
            implanted as fiducial markers in or near the tumor.  The dataset is most fully described in detail in Balik et al.1  Seven of
            the subjects had gold coils implanted as fiducial markers in or near the tumor. The implantation procedure and details of marker
            location are described in detail in Roman et al.2 References
          </p>
        </div>
      ),
    },
    {
      key: '2',
      label: '数据访问',
      children: (
        <div className="drawer-component__detail">
          <p className="drawer-component__detail-text">数据访问相关内容将在这里显示。</p>
        </div>
      ),
    },
    {
      key: '3',
      label: '其他资源',
      children: (
        <div className="drawer-component__detail">
          <p className="drawer-component__detail-text">其他资源相关内容将在这里显示。</p>
        </div>
      ),
    },
    {
      key: '4',
      label: '引用和数据使用政策',
      children: (
        <div className="drawer-component__detail">
          <p className="drawer-component__detail-text">引用和数据使用政策相关内容将在这里显示。</p>
        </div>
      ),
    },
    {
      key: '5',
      label: '其他信息',
      children: (
        <div className="drawer-component__detail">
          <p className="drawer-component__detail-text">其他信息相关内容将在这里显示。</p>
        </div>
      ),
    },
  ];

  /**
   * 处理关闭事件
   */
  const handleClose = () => {
    onClose?.();
    onCancel?.();
  };

  /**
   * 处理确认事件
   */
  const handleOk = () => {
    onOk?.();
  };

  return (
    <Drawer
      open={visible}
      title={title}
      footer={null}
      width={width}
      mask={mask}
      maskClosable={maskClosable}
      onClose={handleClose}
      className="drawer-component"
      // wrapClassName="drawer-component__wrapper"
      destroyOnClose
    >
      <div className="drawer-component__container">
        {/* 内容区域 */}
        <div className="drawer-component__content">
          {content || (
            <>
              {/* 标题部分 */}
              <div className="drawer-component__content-header">
                <h2 className="drawer-component__content-title">4D-LungData from 4D Lung Imagingof NSCL C Patients</h2>
                <div className="drawer-component__doi">
                  <span className="drawer-component__doi-label">DOI:</span>
                  <span className="drawer-component__doi-link">10.7937/K9/TCIA.2016.ELN8YGLE</span>
                </div>
                <div className="drawer-component__meta">
                  <span className="drawer-component__meta-item">
                    <Image src={image_1749625978675_jzgl86} preview={false} /> 2025-04-30
                  </span>
                  <span className="drawer-component__meta-item">
                    <Image src={image_1749625972548_1qi5fp} preview={false} /> 1286
                  </span>
                  <span className="drawer-component__meta-item">
                    <Image src={image_1749625966598_ckpjxz} preview={false} /> 389
                  </span>
                  <span className="drawer-component__meta-item">
                    <Image src={image_1749625960270_1e8drs} preview={false} /> 166
                  </span>
                </div>
              </div>

              {/* 表格部分 */}
              <div className="drawer-component__table-section">
                <Table dataSource={tableData} columns={columns} pagination={false} size="small" className="drawer-component__table" />
              </div>

              {/* 使用 Ant Design Tabs 组件 */}
              <Tabs defaultActiveKey="1" items={items} className="drawer-component__tabs" />
            </>
          )}
        </div>

        {/* 底部区域 */}
        <div className="drawer-component__footer">
          <div className="drawer-component__footer-left">
            <Image src={image_1749625984405_mnwt2g} preview={false} />
            <span className="drawer-component__translate-text">翻译</span>
            <Switch size="small" />
          </div>
          <Button type="primary" icon={<DownloadOutlined />} className="drawer-component__download-btn">
            下载(183.04GB)
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default DrawerComponent;
