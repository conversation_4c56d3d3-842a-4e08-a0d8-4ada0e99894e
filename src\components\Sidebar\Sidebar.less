// 变量定义
@sidebar-width: 280px;
@sidebar-collapsed-width: 80px;
@primary-color: var(--ant-primary-color);
@text-color: rgba(0, 0, 0, 0.85);
@menu-item-height: 40px;
@border-radius: 4px;
@gradient-start: rgba(230, 247, 255, 0.95); // 浅蓝色起始
@gradient-end: rgba(255, 255, 255, 0.95); // 白色结束

// 侧边栏容器
.sidebar-container {
  position: relative;
  width: @sidebar-width;
  min-width: @sidebar-collapsed-width;
  max-width: @sidebar-width;
  height: 100vh;
  background: linear-gradient(0deg, #f9fafb 60.62%, #e2f1fe 100.96%);
  display: flex;
  flex-direction: column;
  padding: 20px;
  transition: width 0.3s ease;

  &.collapsed {
    width: @sidebar-collapsed-width;

    .sidebar-header {
      padding: 0;
      justify-content: center;
    }

    .sidebar-menu {
      .ant-menu {
        width: @sidebar-collapsed-width - 40px;
        .ant-menu-submenu {
          margin-bottom: 20px;
          .ant-menu-item-icon {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }

    .sidebar-footer {
      .user-info {
        justify-content: center;

        .user-avatar {
          margin-right: 0;
        }
      }
    }
  }
}

// Logo区域
.sidebar-header {
  height: 36px;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .logo-icon {
    width: 155px;
    height: 27px;
    background: url('../../images/nns8rmfz.svg') no-repeat center;
    background-size: contain;
    cursor: pointer;
  }
  img {
    display: block;
    width: 20px;
    height: 20px;
    object-fit: cover;
    cursor: pointer;
  }
}
.sidebar-line {
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  margin: 22px 0;
}

// 菜单区域
.sidebar-menu {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;

  .custom-menu {
    background: transparent;
    border-right: none;
    font-size: 18px;

    // 重置ant-menu的默认样式
    .ant-menu-sub.ant-menu-inline {
      background: transparent;
    }

    .ant-menu-item {
      color: rgba(0, 0, 0, 0.65);
      font-weight: 600;

      &:hover {
        color: @primary-color;
      }

      &.ant-menu-item-selected {
        color: @primary-color;
        background-color: transparent;

        &::after {
          display: none;
        }
      }
    }

    // 子菜单样式
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        position: relative;
        margin-top: 0;
        margin-bottom: 8px;
        padding: 0 !important;
        font-weight: 600;

        &:hover {
          color: @primary-color;
        }
      }
    }

    // 展开箭头
    .anticon {
      color: rgba(0, 0, 0, 0.45);
    }

    .ant-menu-sub.ant-menu-inline > .ant-menu-item,
    .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      height: 44px;
      line-height: 44px;
      padding-left: 42px !important;
      font-size: 16px;
    }
    .ant-menu-vertical > .ant-menu-item,
    .ant-menu-vertical-left > .ant-menu-item,
    .ant-menu-vertical-right > .ant-menu-item,
    .ant-menu-inline > .ant-menu-item,
    .ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      height: 44px;
      line-height: 44px;
    }

    .ant-menu-vertical .ant-menu-item:not(:last-child),
    .ant-menu-vertical-left .ant-menu-item:not(:last-child),
    .ant-menu-vertical-right .ant-menu-item:not(:last-child),
    .ant-menu-inline .ant-menu-item:not(:last-child) {
      margin-bottom: 0;
    }

    .ant-menu-vertical .ant-menu-item,
    .ant-menu-vertical-left .ant-menu-item,
    .ant-menu-vertical-right .ant-menu-item,
    .ant-menu-inline .ant-menu-item,
    .ant-menu-vertical .ant-menu-submenu-title,
    .ant-menu-vertical-left .ant-menu-submenu-title,
    .ant-menu-vertical-right .ant-menu-submenu-title,
    .ant-menu-inline .ant-menu-submenu-title {
      margin-top: 0;
    }

    .ant-menu-submenu-selected {
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .menu-icon-1 {
    width: 32px;
    height: 32px;
    background: url('../../images/9txppqq2.svg') no-repeat center;
    background-size: contain;
  }

  .menu-icon-2 {
    width: 32px;
    height: 32px;
    background: url('../../images/yqd3u11m.svg') no-repeat center;
    background-size: contain;
  }

  .menu-icon-3 {
    width: 32px;
    height: 32px;
    background: url('../../images/k2asoyjm.svg') no-repeat center;
    background-size: contain;
  }
}

// 滚动条样式
.sidebar-menu::-webkit-scrollbar {
  width: 6px;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #e8e8e8;
  border-radius: 3px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

// 底部用户信息区域
.sidebar-footer {
  position: relative;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: #f0f3f7;
  cursor: pointer;

  .user-info {
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: @border-radius;

    .username {
      flex: 1;
      font-size: 16px;
      margin-left: 6px;
      color: @text-color;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-dropdown-icon {
      font-size: 16px;
      color: @text-color;
      transition: transform 0.3s ease;
      cursor: pointer;

      &:hover {
        color: @primary-color;
      }
    }
  }
  .user-menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f0f3f7;
    border-radius: 12px 12px 0px 0px;
    padding: 8px 8px 0px 8px;
    .user-menu-item {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 48px;
      padding: 0 12px;
      border-radius: 6px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      transition: background-color 0.3s ease;
      cursor: pointer;
      span {
        font-size: 16px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.65);
      }
    }
    .user-menu-item:hover {
      background-color: #e6eaf2;
    }
    .user-menu-item:last-child {
      color: #ff3d40;
    }
  }
}
