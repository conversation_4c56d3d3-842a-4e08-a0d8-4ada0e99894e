.inference-service-edit-modal {
  .ant-modal-body {
    max-height: 80vh;
    overflow-y: auto;
  }

  .inference-deploy-form {
    .form-section {
      margin-bottom: 8px;

      .section-title {
        position: relative;
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
        padding-left: 8px;
        line-height: 1.5;
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          content: '';
          display: inline-block;
          width: 3px;
          height: 16px;
          background-color: var(--ant-primary-color);
          border-radius: 9px;
          margin-right: 8px;
        }
      }

      .ant-form-item {
        margin-bottom: 8px;
      }

      .replica-form-item {
        .ant-form-item-control-input-content {
          .replica-row {
            display: flex;
            align-items: flex-end;
            gap: 12px;

            .replica-item {
              flex: 1;
              margin-bottom: 0;

              .replica-input-group {
                display: flex;
                align-items: center;
                gap: 12px;

                .replica-label {
                  font-size: 16px;
                  color: rgba(0, 0, 0, 0.25);
                  min-width: 30px;
                }
              }
            }

            .replica-separator {
              line-height: 48px;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.45);
            }
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding-top: 24px;
    }
  }
}
