// 整体容器样式
.task-management-container {
  height: 100%;
  padding: 40px 60px;
  background-color: #fff;
  overflow: hidden;

  .header-container {
    position: relative;
    margin-bottom: 16px;
    .ant-tabs-tab {
      font-size: 18px;
      font-weight: 600;
    }

    .view-type-segmented {
      background-color: #f5f7fa;
      position: absolute;
      right: 0;
      bottom: 16px;
      .ant-segmented-item-label {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 48px;
        min-height: 48px;
        line-height: 1;
      }
    }
  }

  .search-area {
    margin-bottom: 28px;

    .search-item {
      width: 100%;

      .ant-input,
      .ant-select,
      .ant-picker {
        width: 100%;
      }
    }

    .button-group {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;
      .ant-btn {
        width: 96px;
        height: 48px;
      }
    }
  }

  .table-area {
    .pagination-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 40px;
      padding-top: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;
      .pagination-total {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }

    // 卡片视图样式
    .card-view {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
      gap: 16px;
      padding: 16px 10px 0 0;
      max-height: calc(100vh - 300px);
      overflow-y: auto;

      .task-card {
        height: 100%;
        transition: all 0.3s;
        background-color: #f5f7fa;
        border-radius: 8px;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .task-card-content {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }
        .card-header {
          display: flex;
          gap: 12px;
          .task-image {
            img {
              width: 96px;
              height: 96px;
              object-fit: cover;
              border-radius: 8px;
            }
          }

          .task-info {
            width: calc(100% - 96px);
            .title {
              width: 100%;
              margin: 0 0 8px;
              font-size: 16px;
              font-weight: 600;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .task-flow {
              color: rgba(0, 0, 0, 0.45);
              margin-bottom: 16px;
            }

            .tags-container {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;

          .action-buttons {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
  }

  // 覆盖 antd 默认样式
  // 基础表单控件统一样式
  .ant-input,
  .ant-picker {
    height: 48px !important;
    font-size: 14px;
    line-height: normal !important;
  }
  .ant-input-affix-wrapper {
    padding: 0 16px !important;
  }

  // Select 样式(不包括分页器)
  .ant-select:not(.ant-pagination-options-size-changer) {
    height: 48px !important;
    .ant-select-selector {
      height: 48px !important;
      display: flex;
      align-items: center;
    }
  }
  // 按钮
  .ant-btn.ant-btn-link {
    padding: 0;
  }

  // 表格样式
  .ant-table {
    table {
      border-radius: 2px 2px 0 0;
    }

    .ant-table-thead > tr > th {
      padding: 0 16px !important;
      background: #f5f7fa;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
      font-weight: 600;
      line-height: 60px !important;
    }

    .ant-table-tbody > tr > td {
      padding: 0 16px !important;
      line-height: 60px !important;
      .task-flow-link {
        color: #1890ff;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  .ant-btn.ant-gray {
    height: 36px;
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid #e6eaf2;
    background-color: #f5f7fa;
    text-shadow: none;
    box-shadow: none;
  }
}

.status-dot {
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
  }

  &.Pending {
    color: #faad14;
  }
  &.Running {
    color: #1890ff;
  }
  &.Succeeded {
    color: #52c41a;
  }
  &.Failed {
    color: #ff4d4f;
  }
  &.Terminated {
    color: #bfbfbf;

    .training-status {
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
