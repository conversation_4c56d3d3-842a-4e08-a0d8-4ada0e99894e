import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select, Button, message, Spin } from 'antd';
import {
  getInferenceServiceModelViewDetail,
  updateInferenceServiceModelView,
  getInferenceServiceModelViewInfo,
} from '../../api/kubeflowApi';
import './InferenceServiceEdit.less';

const { Option } = Select;

interface InferenceServiceEditProps {
  visible: boolean;
  serviceId: string | null;
  onCancel: () => void;
  onSuccess: () => void;
}

interface EditFormData {
  serviceType: string;
  projectGroup: string;
  label: string;
  memoryRequest: number;
  cpuRequest: number;
  gpuRequest: number;
  minReplicas: number;
  maxReplicas: number;
}

const InferenceServiceEdit: React.FC<InferenceServiceEditProps> = ({ visible, serviceId, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formConfig, setFormConfig] = useState<any>(null);
  const [configLoading, setConfigLoading] = useState(false);

  // 获取表单配置信息
  const fetchFormConfig = async () => {
    setConfigLoading(true);
    try {
      const response = await getInferenceServiceModelViewInfo();
      if (response.status === 200 && response.data) {
        setFormConfig(response.data);
      }
    } catch (error: any) {
      console.error('获取表单配置失败:', error);
    } finally {
      setConfigLoading(false);
    }
  };

  // 获取详情数据并填充表单
  const fetchAndFillForm = async () => {
    if (!serviceId) return;

    setLoading(true);
    try {
      const response = await getInferenceServiceModelViewDetail(serviceId, {});
      if (response.status === 200 && response.data) {
        const data = response.data.result || response.data;

        // 转换数据格式以匹配新的表单结构
        const memoryValue = data.resource_memory ? parseInt(data.resource_memory.replace('G', '')) : 5;
        const cpuValue = data.resource_cpu ? parseInt(data.resource_cpu) : 5;
        const gpuValue = data.resource_gpu ? parseInt(data.resource_gpu) : 0;

        form.setFieldsValue({
          serviceType: data.service_type || 'serving',
          projectGroup: data.project?.id || data.project || '',
          label: data.label,
          memoryRequest: memoryValue,
          cpuRequest: cpuValue,
          gpuRequest: gpuValue,
          minReplicas: data.min_replicas || 1,
          maxReplicas: data.max_replicas || 1,
        });
      } else {
        message.error('获取服务信息失败');
      }
    } catch (error: any) {
      console.error('获取服务信息失败:', error);
      message.error('获取服务信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchFormConfig();
      if (serviceId) {
        fetchAndFillForm();
      }
    }
  }, [visible, serviceId]);

  // 提交修改
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 构建更新数据，使用接口需要的字段名
      const updateData = {
        service_type: values.serviceType,
        project: values.projectGroup,
        label: values.label,
        resource_memory: `${values.memoryRequest}G`,
        resource_cpu: values.cpuRequest.toString(),
        resource_gpu: values.gpuRequest.toString(),
        min_replicas: values.minReplicas,
        max_replicas: values.maxReplicas,
      };

      const response = await updateInferenceServiceModelView(serviceId!, updateData);
      if (response.status === 200) {
        message.success('修改成功');
        onSuccess();
      } else {
        message.error(response.data?.message || '修改失败');
      }
    } catch (error: any) {
      if (error?.errorFields) {
        message.error('请检查表单输入');
      } else {
        console.error('修改失败:', error);
        message.error('修改失败');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="修改推理服务"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      className="inference-service-edit-modal common-modal"
      destroyOnClose
    >
      <Spin spinning={loading || configLoading}>
        <Form form={form} layout="horizontal" className="inference-deploy-form" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          {/* 模型设置 */}
          <div className="form-section">
            <h3 className="section-title">模型设置</h3>

            <Form.Item label="服务类型" name="serviceType" rules={[{ required: true, message: '请选择服务类型' }]}>
              <Select placeholder="请选择服务类型">
                {formConfig?.add_columns
                  ?.find((col: any) => col.name === 'service_type')
                  ?.values?.map((option: any) => (
                    <Option key={option.id} value={option.value}>
                      {option.value}
                    </Option>
                  ))}
              </Select>
            </Form.Item>

            <Form.Item label="项目组" name="projectGroup" rules={[{ required: true, message: '请选择项目组' }]}>
              <Select placeholder="请选择项目组">
                {formConfig?.add_columns
                  ?.find((col: any) => col.name === 'project')
                  ?.values?.map((option: any) => (
                    <Option key={option.id} value={option.id}>
                      {option.value}
                    </Option>
                  ))}
              </Select>
            </Form.Item>

            <Form.Item label="标签" name="label" rules={[{ required: true, message: '请输入标签' }]}>
              <Input placeholder="请输入标签" />
            </Form.Item>
          </div>

          {/* 部署推理设置 */}
          <div className="form-section">
            <h3 className="section-title">部署推理设置</h3>

            <Form.Item label="内存申请" name="memoryRequest" rules={[{ required: true, message: '请输入内存申请量' }]}>
              <InputNumber min={1} max={64} placeholder="请输入内存申请量" addonAfter="G" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="CPU申请" name="cpuRequest" rules={[{ required: true, message: '请输入CPU申请量' }]}>
              <InputNumber min={1} max={32} placeholder="请输入CPU申请量" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="GPU申请" name="gpuRequest" rules={[{ required: true, message: '请输入GPU申请量' }]}>
              <InputNumber min={0} max={8} placeholder="请输入GPU申请量" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="副本数" className="replica-form-item">
              <div className="replica-row">
                <Form.Item name="minReplicas" rules={[{ required: true, message: '请输入最小副本数' }]} className="replica-item">
                  <div className="replica-input-group">
                    <span className="replica-label">Min</span>
                    <InputNumber min={1} max={10} placeholder="1" style={{ width: '100%' }} />
                  </div>
                </Form.Item>

                <span className="replica-separator">-</span>

                <Form.Item name="maxReplicas" rules={[{ required: true, message: '请输入最大副本数' }]} className="replica-item">
                  <div className="replica-input-group">
                    <span className="replica-label">Max</span>
                    <InputNumber min={1} max={10} placeholder="1" style={{ width: '100%' }} />
                  </div>
                </Form.Item>
              </div>
            </Form.Item>
          </div>

          {/* 底部按钮 */}
          <div className="form-actions">
            <Button onClick={handleCancel} className="cancel-btn">
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} loading={submitting} className="submit-btn">
              保存修改
            </Button>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default InferenceServiceEdit;
