import React from 'react';
import './FooterBar.less';
import nns8rmfz from '../../images/nns8rmfz.svg';

const FooterBar: React.FC = () => {
  // 产品跳转处理函数
  const handleProductNavigation = (productName: string) => {
    switch (productName) {
      case '深度问数':
        window.open('http://wenshu.aethermind.cn', '_blank');
        break;
      case '训推一体化方案':
        window.open('http://lab.aethermind.cn', '_blank');
        break;
      default:
        // 其他产品不进行跳转
        break;
    }
  };

  return (
    <div className="footer-bar">
      <div className="footer-bar__container">
        <div className="footer-bar__col footer-bar__logo-col">
          <img src={nns8rmfz} alt="汇智灵曦" className="footer-bar__logo" />
        </div>
        <div className="footer-bar__col">
          <div className="footer-bar__col-title">产品</div>
          <div className="footer-bar__item" onClick={() => handleProductNavigation('深度问数')}>
            深度问数
          </div>
          <div className="footer-bar__item">灵曦助手</div>
          <div className="footer-bar__item" onClick={() => handleProductNavigation('训推一体化方案')}>
            训推一体化方案
          </div>
          <div className="footer-bar__item">智慧影像</div>
          <div className="footer-bar__item">病理交互式大模型</div>
        </div>
        <div className="footer-bar__col">
          <div className="footer-bar__col-title">法律&安全</div>
          <div className="footer-bar__item" onClick={() => window.open('/frontend/privacy', '_blank')}>
            隐私政策
          </div>
          <div className="footer-bar__item" onClick={() => window.open('/frontend/terms', '_blank')}>
            用户协议
          </div>
          <div className="footer-bar__item">反馈安全漏洞</div>
        </div>
        <div className="footer-bar__col">
          <div className="footer-bar__col-title">加入我们</div>
          <div className="footer-bar__item">公司详情</div>
        </div>
      </div>
      {/* 备案号 */}
      <div className="footer-beian">
        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">
          沪ICP备2025134180号
        </a>
      </div>
    </div>
  );
};

export default FooterBar;
