.inference-service-page {
  height: 100%;
  padding: 40px 60px;
  background-color: #fff;
  overflow: hidden;

  .header-container {
    position: relative;
    margin-bottom: 28px;
    border-bottom: 1px solid #e6eaf2;
    h5 {
      height: 48px;
      line-height: 48px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 0;
    }
  }

  .search-area {
    margin-bottom: 28px;

    .search-item {
      width: 100%;

      .ant-input,
      .ant-select,
      .ant-picker {
        width: 100%;
      }
    }
  }

  .table-area {
    .pagination-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 40px;
      padding-top: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;
      .pagination-total {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .batch-operation {
      width: 100%;
      height: 80px;
      display: flex;
      align-items: center;
    }
  }

  .operation-button {
    padding: 4px 8px;
    min-width: auto;

    &:hover {
      background-color: rgba(0, 0, 0, 0.025);
    }

    // 更多操作按钮样式
    &.more-button {
      padding: 4px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 状态标签特殊样式
  .ant-tag {
    &.ant-tag-green {
      background: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.ant-tag-blue {
      background: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.ant-tag-red {
      background: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }

    &.ant-tag-orange {
      background: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
  }

  // 覆盖 antd 默认样式
  // 基础表单控件统一样式
  .ant-input,
  .ant-picker {
    height: 48px !important;
    font-size: 14px;
    line-height: normal !important;
  }
  .ant-input-affix-wrapper {
    height: 48px !important;
    font-size: 14px;
    line-height: normal !important;

    .ant-input {
      height: 100% !important;
      padding: 0 !important;
    }
  }

  // Select 样式(不包括分页器)
  .ant-select:not(.ant-pagination-options-size-changer) {
    height: 48px !important;
    .ant-select-selector {
      height: 48px !important;
      display: flex;
      align-items: center;
    }
  }
  // 按钮
  .ant-btn.ant-btn-link {
    padding: 0;
  }

  // 表格样式
  .ant-table {
    table {
      border-radius: 2px 2px 0 0;
    }

    .ant-table-thead > tr > th {
      padding: 0 16px !important;
      background: #f5f7fa;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
      font-weight: 600;
      line-height: 60px !important;
    }

    .ant-table-tbody > tr {
      min-height: 60px;
    }

    .ant-table-tbody > tr > td {
      line-height: 1.5 !important;
      vertical-align: middle;

      // 对于包含compact-cell的单元格，调整内边距
      .compact-cell {
        line-height: 1.5;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;

        > div {
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .compact-cell-content {
          display: flex;
          align-items: center;
          gap: 8px;
          .compact-cell-label {
            display: flex;
            align-items: center;
            gap: 4px;
            color: rgba(0, 0, 0, 0.25);
            font-size: 12px;
            img {
              width: 12px;
              height: 12px;
            }
          }
          .compact-cell-value {
            color: rgba(0, 0, 0, 0.85);
            font-size: 12px;
          }
        }
      }
    }
  }
  .ant-btn {
    height: 48px;
  }
  .ant-btn.ant-gray {
    height: 48px;
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid #e6eaf2;
    background-color: #fff;
    text-shadow: none;
    box-shadow: none;
  }
}
