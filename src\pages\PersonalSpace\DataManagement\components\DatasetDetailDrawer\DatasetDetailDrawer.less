// 自定义抽屉组件样式
.custom-drawer {
  // 抽屉容器
  &__container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  // 内容区域
  &__content {
    flex: 1;
    overflow-y: auto;
  }

  // 字段容器
  &__field {
    display: flex;
    margin-bottom: 24px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 字段标签
  &__label {
    flex-shrink: 0;
    width: 80px;
    margin-right: 28px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    text-align: right;
  }

  // 字段值
  &__value {
    flex: 1;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    word-break: break-all;

    // 文本类型值
    &--text {
      font-weight: 400;
    }

    // 描述类型值
    &--description {
      line-height: 20px;
      word-wrap: break-word;
      white-space: pre-wrap;
    }
  }

  // 预览图
  &__preview-image {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    object-fit: cover;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }

  // 标签容器
  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  // 文件容器
  &__file {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  // 文件名
  &__file-name {
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
