// 全局防滚动样式 - 当移动端页面显示时
body.mobile-no-scroll,
html.mobile-no-scroll {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
  overscroll-behavior: none !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: auto !important;
}

// 移动端访问页面样式
.mobile-access-page {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;

  // 完全禁止滚动
  overflow: hidden !important;
  overscroll-behavior: none !important;

  // 移动端优化
  -webkit-overflow-scrolling: touch;
  -webkit-user-select: none;
  user-select: none;

  // 防止页面缩放和滚动
  touch-action: none !important;

  .mobile-card {
    max-width: 360px;
    width: calc(100% - 32px);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
    margin: 0 auto;

    // 卡片动画
    animation: slideUp 0.6s ease-out;

    // 移动端触摸优化
    -webkit-tap-highlight-color: transparent;

    // 确保卡片内容不会溢出
    max-height: calc(100vh - 80px);

    .mobile-content {
      text-align: center;
      padding: 24px 20px;

      // 文字选择优化
      -webkit-user-select: text;
      user-select: text;

      // 确保内容不会溢出
      overflow: hidden;

      .icon-container {
        margin-bottom: 24px;

        .warning-icon {
          font-size: 56px;
          color: #faad14;
          margin-bottom: 16px;
          display: block;
        }

        .title {
          margin: 0;
          color: #262626;
          font-size: 20px;
        }
      }

      .description {
        font-size: 16px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 24px;
        text-align: center;
      }

      .device-info {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;

        .info-content {
          font-size: 14px;
          color: #666;
          text-align: left;

          .info-item {
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            &:last-child {
              margin-bottom: 0;
            }

            .info-icon {
              margin-right: 10px;
              color: #1890ff;
              font-size: 16px;
            }
          }
        }
      }

      .recommendation {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px;
        color: white;
        text-align: center;

        .main-text {
          font-size: 15px;
          color: white;
          display: block;
          line-height: 1.5;
          font-weight: 500;
        }

        .sub-text {
          font-size: 13px;
          color: rgba(255, 255, 255, 0.8);
          display: block;
          margin-top: 8px;
          line-height: 1.4;
        }
      }
    }
  }

  .mobile-footer {
    margin-top: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    text-align: center;

    // 底部文字动画
    animation: fadeIn 1s ease-out 0.3s both;
  }
}

// 动画定义
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 移动端媒体查询
@media (max-width: 480px) {
  .mobile-access-page {
    padding: 16px !important;

    .mobile-card {
      max-width: calc(100% - 32px) !important;
      width: calc(100% - 32px) !important;

      .ant-card-body {
        padding: 20px 16px !important;
      }

      .mobile-content {
        padding: 20px 16px !important;
      }
    }
  }
}

// 超小屏幕适配
@media (max-width: 360px) {
  .mobile-access-page {
    padding: 12px !important;

    .mobile-card {
      max-width: calc(100% - 24px) !important;
      width: calc(100% - 24px) !important;

      .mobile-content {
        padding: 16px 12px !important;
      }
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 600px) {
  .mobile-access-page {
    .mobile-card {
      max-width: 480px !important;
      max-height: calc(100vh - 40px) !important;

      .mobile-content {
        padding: 16px 20px !important;
      }
    }

    .mobile-footer {
      margin-top: 8px !important;
      font-size: 11px !important;
    }
  }
}

// 极小高度适配
@media (max-height: 500px) {
  .mobile-access-page {
    .mobile-card {
      .mobile-content {
        padding: 12px 16px !important;

        .ant-typography-title {
          font-size: 18px !important;
          margin-bottom: 12px !important;
        }

        .ant-typography {
          margin-bottom: 16px !important;
        }
      }
    }
  }
}
