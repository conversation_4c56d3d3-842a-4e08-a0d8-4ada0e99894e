import { message } from 'antd';

/**
 * 使用fetch API处理重定向的备选方案
 * @param url - 请求URL
 * @param method - HTTP方法
 * @param data - 请求数据
 * @param successMessage - 成功消息
 * @param errorMessage - 错误消息
 * @param enableRedirect - 是否启用重定向处理，默认为true
 */
export const fetchWithRedirectHandling = async (
  url: string,
  method: string = 'GET',
  data?: any,
  successMessage?: string,
  errorMessage?: string,
  enableRedirect: boolean = true,
): Promise<{ success: boolean; redirectUrl?: string; response?: any }> => {
  try {
    const token = localStorage.getItem('myapp_token');

    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/html, */*',
        'X-Requested-With': 'XMLHttpRequest',
        ...(token && { Authorization: token }),
      },
      redirect: enableRedirect ? 'manual' : 'follow', // 根据开关控制重定向处理
      ...(data && method !== 'GET' && { body: JSON.stringify(data) }),
    };

    console.log('发送fetch请求到:', url);
    const response = await fetch(url, options);

    console.log('Fetch响应:', response);

    // 检查是否是重定向响应
    if (response.redirected) {
      window.open(response.url, '_blank');
      if (successMessage) {
        message.success(successMessage);
      }

      return { success: true, response };
    }

    // 检查是否是opaqueredirect类型（被浏览器拦截的重定向）
    if (response.type === 'opaqueredirect') {
      console.log('检测到opaque重定向');

      if (successMessage) {
        message.success(successMessage);
      }

      return { success: true, response };
    }

    // 处理错误状态
    console.error('Fetch请求失败:', response.status, response.statusText);
    if (errorMessage) {
      message.error(`${errorMessage}: ${response.status} ${response.statusText}`);
    }

    return { success: false, response };
  } catch (error) {
    console.error('Fetch请求异常:', error);
    if (errorMessage) {
      message.error(errorMessage);
    }
    return { success: false };
  }
};

/**
 * 专门用于推理服务调试的方法
 */
export const debugInferenceServiceWithXHR = async (serviceId: string) => {
  const url = `${process.env.REACT_APP_BASE_URL}/inferenceservice_modelview/api/deploy/debug/${serviceId}`;
  return fetchWithRedirectHandling(url, 'GET', undefined, '', '调试失败', false);
};

/**
 * 专门用于推理服务部署的方法
 */
export const deployInferenceServiceWithXHR = async (serviceId: string) => {
  const url = `${process.env.REACT_APP_BASE_URL}/inferenceservice_modelview/api/deploy/prod/${serviceId}`;
  return fetchWithRedirectHandling(url, 'GET', undefined, '部署成功', '部署失败', true);
};

/**
 * 专门用于推理服务清理的方法
 */
export const clearInferenceServiceWithXHR = async (serviceId: string) => {
  const url = `${process.env.REACT_APP_BASE_URL}/inferenceservice_modelview/api/clear/${serviceId}`;
  return fetchWithRedirectHandling(url, 'GET', undefined, '清理成功', '清理失败', true);
};

/**
 * 通用的GET请求，支持重定向处理
 * @param endpoint - API端点
 * @param successMessage - 成功消息
 * @param errorMessage - 错误消息
 * @param enableRedirect - 是否启用重定向处理，默认为true
 */
export const getWithXHR = async (endpoint: string, successMessage?: string, errorMessage?: string, enableRedirect: boolean = true) => {
  const url = `${process.env.REACT_APP_BASE_URL}${endpoint}`;
  return fetchWithRedirectHandling(url, 'GET', undefined, successMessage, errorMessage, enableRedirect);
};
