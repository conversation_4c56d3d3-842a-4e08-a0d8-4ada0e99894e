// Blocked aria-hidden on a ＜div＞ element 解决方案
.ant-modal div[aria-hidden='true'] {
  display: none !important;
}

// 覆盖antd组件默认样式
.ant-btn,
.ant-input,
.ant-picker,
.ant-select-selector,
.ant-input-affix-wrapper,
.ant-notification-notice,
.ant-message-notice-content {
  border-radius: 4px !important;
}
.ant-popover-inner {
  border-radius: 12px !important;
}
.ant-popover-inner-content {
  padding: 16px !important;
}
.ant-btn-text:hover,
.ant-btn-text:focus {
  background: var(rgba(0, 0, 0, 0.04)) !important;
  backdrop-filter: blur(25px) !important;
}
.ant-modal-header {
  padding: 24px 24px 0 24px !important;
  border-bottom: none; // 移除 Ant Design 默认的 header 底部边框
  .ant-modal-title {
    font-size: 18px !important;
    font-weight: 500 !important;
    color: rgba(0, 0, 0, 0.85) !important;
  }
}

.ant-modal-content {
  border-radius: 20px !important;
  overflow: hidden !important;
}
.ant-tag {
  line-height: 24px !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  padding: 0 8px !important;
  margin: 0 !important;
}
.ant-image {
  line-height: 1 !important;
}
.ant-dropdown-menu {
  padding: 8px !important;
  border-radius: 12px !important;
}

.common-modal {
  .ant-modal-body {
    display: flex;
    flex-direction: column;
    gap: 24px; // 各部分之间间距
  }

  .ant-modal-footer {
    border-top: none; // 移除 Ant Design 默认的 footer 顶部边框
    padding-top: 0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding-right: 24px;
    padding-bottom: 24px;
  }
  // input 样式
  .ant-input:not(textarea) {
    height: 48px;
  }
  // Select 样式
  .ant-select {
    min-height: 48px;
    .ant-select-selector {
      min-height: 48px;
      display: flex;
      align-items: center;
    }
  }
  // 按钮
  .ant-btn:not(.ant-upload-list-item-card-actions-btn) {
    height: 48px;
    min-width: 120px; // 按钮最小宽度
    border-radius: 4px;
  }
  // 日期选择器
  .ant-picker {
    height: 48px;
  }
  // 数字输入框
  .ant-input-number {
    height: 48px;
  }
  .ant-input-number-input {
    height: 48px;
  }
  // 表单label
  .ant-form-item-label > label {
    height: 48px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 600;
    font-size: 16px;
  }
  // 表单项间距
  .ant-form-item {
    margin-bottom: 0;
  }
}
.common-drawer {
  .ant-drawer-title {
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-drawer-footer {
    border-top: none; // 移除 Ant Design 默认的 footer 顶部边框
    padding-top: 0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding-right: 24px;
    padding-bottom: 24px;
  }

  // 按钮
  .ant-btn {
    height: 48px;
    min-width: 120px; // 按钮最小宽度
    border-radius: 4px;
    background-color: #e6eaf2;
  }
}
