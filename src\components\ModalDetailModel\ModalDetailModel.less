// MedicalModal.less
// 医学影像弹窗组件样式文件
// 遵循 BEM 命名规范

.medical-modal {
  min-width: 800px;
  .ant-modal-body {
    padding: 40px;
  }

  // 主容器
  &__container {
    width: 100%;
    min-height: 600px;
    background-color: #ffffff;
  }

  // 右侧信息区域
  &__info-section {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
  }
  &__item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
    &:first-child {
      margin-bottom: 12px;
    }
    &:nth-child(2) {
      margin-bottom: 20px;
    }
  }
  &__name {
    width: 55%;
    display: flex;
    align-items: center;
    gap: 20px;
  }
  &__icon {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    flex-shrink: 0;
  }

  // 标题区域
  &__header {
    height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    line-height: 1.3;
  }

  // 描述区域
  &__description {
    width: 55%;
  }

  &__desc-text {
    font-size: 14px;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.45);
    margin: 0;
  }

  // 标签区域
  &__tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  &__tag {
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    padding: 0 12px;
    font-size: 12px;
    border: none;
    background-color: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
  }

  // 操作按钮区域
  &__actions {
    width: 45%;
    min-width: 45%;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__use-btn {
    width: 100%;
    height: 60px;
    padding: 0 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px !important;
    img {
      height: 20px;
      width: 20px;
      object-fit: cover;
      margin-right: 4px;
      vertical-align: sub;
    }
  }
  &__inference-btn {
    width: 100%;

    height: 60px;
    padding: 0 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px !important;
    border-color: #ff8d2f;
    background-color: #ff8d2f;
    // 相近的颜色
    &:hover {
      background-color: #ffa94d;
      border-color: #ffa94d;
    }
    img {
      height: 20px;
      width: 20px;
      object-fit: cover;
      margin-right: 4px;
      vertical-align: sub;
    }
  }

  &__stats {
    width: 45%;
    min-width: 45%;
    display: flex;
    gap: 12px;
  }

  &__stat-item {
    display: flex;
    gap: 4px;
    width: 100%;
    height: 60px;
    padding: 0 20px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    border: 1px solid #e6eaf2;
    background: #f5f7fa;
    img {
      height: 20px;
      width: 20px;
      object-fit: cover;
    }
  }

  // 文件结构区域
  &__file-section {
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 12px;
    background-color: #f5f7fa;
  }

  &__section-title {
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 16px 0;
  }

  &__file-tree {
    max-height: 300px;
    overflow-y: auto;
  }

  &__file-item {
    margin-bottom: 4px;

    &--level-0 {
      font-weight: 600;
      color: #1a1a1a;
    }

    &--level-1 {
      margin-left: 20px;
      color: #666666;
    }

    &--level-2 {
      margin-left: 40px;
      color: #999999;
      font-size: 13px;
    }
  }

  &__file-name {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 2px 0;
    font-size: 14px;
    line-height: 1.4;
  }

  &__file-children {
    margin-top: 4px;
  }

  // 模型信息区域
  &__model-section {
    padding: 20px;
    border-radius: 12px;
    background-color: #f5f7fa;
  }

  &__model-desc {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin: 0;
    line-height: 1.5;
  }

  // 自定义内容区域
  &__custom-content {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
  }
}

// 滚动条样式优化
.medical-modal__file-tree {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
