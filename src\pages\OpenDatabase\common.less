// TCGA 平台样式文件
// 采用 BEM 命名规范，确保样式模块化和可维护性

.database-platform {
  position: relative;
  height: 100%;
  padding: 0 60px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../images/mfqdg9n9.svg') no-repeat;
    background-size: 100% auto;
    z-index: 0;
  }
  // 头部样式
  .marketplace-header-bg {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    height: auto;
    margin-bottom: 60px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    &.hide {
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      height: 0;
      margin: 0;
      overflow: hidden;
    }

    .marketplace-header-content {
      text-align: center;
      margin-top: 80px;

      .marketplace-title {
        font-size: 36px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
      }

      .marketplace-desc {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 32px;
      }

      .marketplace-search-bar {
        display: flex;
        justify-content: center;
        align-items: center;

        .marketplace-search-input {
          width: 600px;
          height: 56px;
          font-size: 16px;
          border-radius: 12px;
          border-color: transparent;
          .anticon {
            color: var(--ant-primary-color);
          }
        }
      }
    }
  }

  // 头部样式
  .marketplace-header {
    position: fixed;
    top: -80px; // 初始位置在屏幕上方
    left: 340px;
    right: 60px;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    transition: top 0.3s ease-in-out;

    &.show {
      top: 0; // 显示时移动到顶部
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .site-title {
        font-size: 24px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .header-right {
      .ant-input-affix-wrapper {
        width: 400px;
        height: 48px;
        border: none;
        background: rgba(255, 255, 255, 0.5);
        .ant-input {
          background-color: transparent;
        }
        .anticon {
          color: var(--ant-primary-color);
        }
      }
    }
  }

  // 主体内容区域
  .main-content {
    position: relative;
    z-index: 1;
    max-width: 1240px;
    margin: 0 auto;
    padding: 12px 24px;
    display: flex;
    gap: 20px;
    border-radius: 16px 16px 0px 0px;
    background-color: #fff;
    &.hide {
      margin-top: 80px;
    }

    // 左侧筛选栏
    .sidebar {
      width: 280px;
      height: calc(100vh - 335px);
      overflow-y: auto;
      flex-shrink: 0;

      .filter-collapse {
        background: #fff;
        border-radius: 12px;
        box-shadow: none;
        overflow: hidden;

        .ant-collapse-header {
          padding: 12px 0 !important;
          font-weight: 600;
          font-size: 16px;
          color: rgba(0, 0, 0, 0.85);
          background: transparent;

          .ant-collapse-expand-icon {
            position: absolute;
            right: 0;
            padding-inline-end: 0;
          }
        }

        .ant-collapse-content-box {
          padding: 0;
        }
      }

      // 疾病类型列表
      .disease-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        .disease-item {
          border: 1px solid #e6eaf2;
          padding: 4px 8px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          transition: all 0.2s ease;
          display: flex;
          justify-content: space-between;
          align-items: center;

          &:hover {
            background: #f5f5f5;
          }

          &.selected {
            background: #f5f7fa;

            .remove-btn {
              font-weight: bold;
              margin-left: 8px;
            }
          }
        }
      }
    }

    // 右侧内容区域
    .content-area {
      flex: 1;
      min-width: 0;

      // 结果统计和控制栏
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        height: 48px;

        .result-info {
          .result-count {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
        }

        .result-controls {
          display: flex;
          align-items: center;
          gap: 24px;

          .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;

            .anticon {
              color: #999;
            }
          }
        }
      }

      // 数据集列表
      .dataset-list {
        height: calc(100vh - 360px);
        padding: 10px;
        overflow-y: auto;
        &.hide {
          height: calc(100vh - 190px);
        }
        .dataset-item {
          background: #fff;
          padding: 10px;
          transition: all 0.3s ease;
          border-bottom: 1px solid #e6eaf2;
          cursor: pointer;
          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            transform: translateY(-2px);
            border-radius: 8px;
          }

          .dataset-header {
            margin-bottom: 8px;
            // 限制显示行数
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            .dataset-title {
              font-size: 16px;
              font-weight: 600;
              color: rgba(0, 0, 0, 0.85);
            }
          }

          .dataset-description {
            margin-bottom: 16px;

            .description-text {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.45);
              // 限制显示行数
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .dataset-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);

            .meta-left {
              display: flex;
              align-items: center;
              gap: 16px;

              .meta-item {
                display: flex;
                align-items: center;
                gap: 4px;
              }
            }
          }
        }
      }
    }
  }
  // Ant Design 组件样式覆盖
  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    padding: 16px 20px !important;
  }

  .ant-tag {
    border-radius: 4px !important;
  }

  .ant-input-affix-wrapper {
    border-radius: 8px !important;
  }
}
