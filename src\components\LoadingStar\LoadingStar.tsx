import React from 'react';
import globalConfig from '../../global.config';
import './LoadingStar.less';

interface LoadingStarProps {
  size?: 'small' | 'default' | 'large';
}

export default function LoadingStar({ size = 'default' }: LoadingStarProps) {
  const sizeMap = {
    small: '96px',
    default: '128px',
    large: '160px',
  };

  return (
    <>
      <img className="loading-cb" src={globalConfig.loadingLogo.default} alt="" style={{ width: sizeMap[size], height: sizeMap[size] }} />
    </>
  );
}
