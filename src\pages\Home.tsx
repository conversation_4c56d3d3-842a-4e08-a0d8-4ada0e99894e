import React, { useState } from 'react';
import { Button, Typography } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import researchSpaceIcon from '../images/9txppqq2.svg';
import databaseIcon from '../images/yqd3u11m.svg';
import image_1753165510059_s4s8zv from '../images/image_1753165510059_s4s8zv.svg';
import nns8rmfz from '../images/nns8rmfz.svg';
import bgImage1 from '../images/image_1749538261849_tzce2x.png';
import bgImage2 from '../images/image_1749538272406_40dznv.png';
import bgVideo from '../images/image_1749535321056_4jx4xi.mp4';
import './Home.less';
import FooterBar from '../components/FooterBar/FooterBar';

// 定义卡片数据类型
interface CardItem {
  title: string;
  description?: string;
  icon: string;
  items?: Array<{
    name: string;
    path?: string;
  }>;
}

// 定义首页组件
const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  // 智研空间卡片数据
  const [researchSpaceData] = useState<CardItem>({
    title: '智研空间',
    description: '集成影像处理与深度学习建模，赋能精准诊疗决策与科研创新。',
    icon: researchSpaceIcon,
    items: [
      { name: 'AI模型商城', path: '/smart-space/ai-model' },
      { name: '图像后处理', path: '/smart-space/image-processing' },
      // { name: '医学统计分析', path: '/smart-space/medical-statistics' },
      // { name: '定量分析', path: '/smart-space/quantitative-analysis' },
    ],
  });

  // 开源数据库卡片数据
  const [databaseData] = useState<CardItem>({
    title: '科研赋能',
    description: '以数据和新技术推动新的AI产品的研究',
    icon: image_1753165510059_s4s8zv,
    items: [
      { name: '深度问数', path: 'external:http://wenshu.aethermind.cn' },
      { name: '灵曦助手' },
      // { name: 'UK Biobank', path: '/open-database/ukBiobank' },
      // { name: 'More', path: '/open-database/more' },
    ],
  });

  // 添加卡片悬浮状态
  const [researchSpaceHovered, setResearchSpaceHovered] = useState(false);
  const [databaseHovered, setDatabaseHovered] = useState(false);

  // 处理卡片项目点击
  const handleItemClick = (path?: string) => {
    if (path) {
      // 检查是否为外部链接
      if (path.startsWith('external:')) {
        const externalUrl = path.replace('external:', '');
        window.open(externalUrl, '_blank');
      } else {
        navigate(path);
      }
    }
  };
  // 立即体验按钮点击
  const handleExperience = () => {
    navigate('/smart-space/ai-model');
  };

  // 渲染卡片项目
  const renderCardItems = (items?: Array<{ name: string; path?: string }>) => {
    if (!items || items.length === 0) return null;

    return (
      <div className="home-page__card-items">
        {items.map((item, index) => (
          <div
            key={index}
            className="home-page__card-item"
            onClick={() => handleItemClick(item.path)}
            style={{ cursor: item.path ? 'pointer' : 'default' }}
          >
            {item.name}
          </div>
        ))}
      </div>
    );
  };

  // 渲染功能卡片
  const renderCard = (data: CardItem, showItems: boolean = false) => {
    return (
      <div
        className="home-page__card"
        style={{ display: showItems ? 'block' : 'flex' }}
        onMouseEnter={() => (data === researchSpaceData ? setResearchSpaceHovered(true) : setDatabaseHovered(true))}
        onMouseLeave={() => (data === researchSpaceData ? setResearchSpaceHovered(false) : setDatabaseHovered(false))}
      >
        <div className="home-page__card-header">
          <div className="home-page__card-title-wrapper">
            {showItems && <img src={data.icon} alt={data.title} className="home-page__card-icon" style={{ marginRight: 12 }} />}
            <Typography.Title level={3} className="home-page__card-title">
              {data.title}
            </Typography.Title>
            <RightOutlined className="home-page__card-arrow" />
          </div>
          {data.description && <Typography.Paragraph className="home-page__card-description">{data.description}</Typography.Paragraph>}
        </div>

        {showItems && renderCardItems(data.items)}

        {!showItems && (
          <div className="home-page__card-icon-wrapper">
            <img src={data.icon} alt={data.title} className="home-page__card-icon" style={{ marginRight: 12 }} />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="home-page">
      {/* 视频背景 */}
      <video className="home-page__background-video" autoPlay muted loop playsInline>
        <source src={bgVideo} type="video/mp4" />
        您的浏览器不支持视频播放。
      </video>
      <div className="home-page__container">
        {/* 顶部Logo和标题区域 */}
        <img src={nns8rmfz} alt="汇智灵曦" className="home-page-logo" />
        {/* 居中标题区 */}
        <div className="home-center-title">
          <div className="home-center-title__main">
            <span>汇智灵曦 · </span>
            <span className="home-center-title__highlight">AI大模型</span>
          </div>
          <div className="home-center-title__subtitle">AI驱动医疗革新，数据融合智启未来</div>
          <div
            className="home-experience-btn-wrapper"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={handleExperience}
          >
            <img src={bgImage2} alt="立即体验" className="home-experience-btn-text" style={{ opacity: isHovered ? 0 : 1 }} />
            <img src={bgImage1} alt="立即体验" className="home-experience-btn-text" style={{ opacity: isHovered ? 1 : 0 }} />
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="home-page__content">
          <div className="home-page__cards">
            <div className="home-page__card-grid">
              {renderCard(researchSpaceData, researchSpaceHovered)}
              {renderCard(databaseData, databaseHovered)}
            </div>
          </div>
        </div>
      </div>
      <FooterBar />
    </div>
  );
};

export default HomePage;
