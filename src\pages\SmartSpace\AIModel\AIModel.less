// AI模型商城样式文件
.ai-marketplace-container {
  position: relative;
  height: 100%;
  padding: 0 60px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../../images/mfqdg9n9.svg') no-repeat;
    background-size: 100% auto;
    z-index: 0;
  }
  // 头部样式
  .marketplace-header-bg {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    height: auto;
    margin-bottom: 60px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    &.hide {
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      height: 0;
      margin: 0;
      overflow: hidden;
    }

    .marketplace-header-content {
      text-align: center;
      margin-top: 80px;

      .marketplace-title {
        font-size: 36px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
      }

      .marketplace-desc {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 32px;
      }

      .marketplace-search-bar {
        display: flex;
        justify-content: center;
        align-items: center;

        .marketplace-search-input {
          width: 600px;
          height: 56px;
          font-size: 16px;
          border-radius: 12px;
          border-color: transparent;
          .anticon {
            color: var(--ant-primary-color);
          }
        }
      }
    }
  }

  // 头部样式
  .marketplace-header {
    position: fixed;
    top: -80px; // 初始位置在屏幕上方
    left: 340px;
    right: 60px;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    transition: top 0.3s ease-in-out;

    &.show {
      top: 0; // 显示时移动到顶部
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .site-title {
        font-size: 24px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .header-right {
      .ant-input-affix-wrapper {
        width: 400px;
        height: 48px;
        border: none;
        background: rgba(255, 255, 255, 0.5);
        .ant-input {
          background-color: transparent;
        }
        .anticon {
          color: var(--ant-primary-color);
        }
      }
    }
  }

  // 主体内容样式
  .marketplace-content {
    position: relative;
    z-index: 1;
    display: flex;
    margin: 0 auto;
    gap: 24px;

    // 侧边栏样式
    .marketplace-sidebar {
      width: 136px;
      height: calc(100vh - 380px);
      overflow-y: auto;
      flex-shrink: 0;
      &.show-header {
        height: calc(100vh - 130px);
      }
      // 优化滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
        background: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.08);
        border-radius: 4px;
        transition: background 0.3s;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.18);
      }
      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .favorites-section {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        .favorites-header {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 12px;

          .favorites-icon {
            font-size: 16px;
          }

          span {
            font-size: 16px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.65);
          }
        }

        .favorites-count {
          color: rgba(0, 0, 0, 0.45);
          font-size: 14px;
        }
      }

      .category-section {
        .section-title {
          padding: 8px 12px;
          font-size: 16px;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.85);
        }

        .category-list {
          .category-item {
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            transition: all 0.2s ease;

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
              color: rgba(0, 0, 0, 0.85);
            }

            &.active {
              background-color: rgba(0, 0, 0, 0.04);
              color: rgba(0, 0, 0, 0.85);
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    // 主要内容区域样式
    .marketplace-main {
      flex: 1;
      height: calc(100vh - 380px);
      overflow-y: auto;
      &.show-header {
        height: calc(100vh - 130px);
      }
      // 优化滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
        background: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.08);
        border-radius: 4px;
        transition: background 0.3s;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.18);
      }
      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .models-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
        gap: 16px;
        padding: 8px 0;

        .model-card {
          border-radius: 12px;
          border: 1px solid #e8eaed;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: var(--ant-primary-color);
            transform: translateY(-2px);
          }

          .ant-card-body {
            padding: 16px;
          }

          .model-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;

            .model-icon {
              width: 64px;
              height: 64px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #ffffff;
              font-size: 20px;
              font-weight: 600;
              flex-shrink: 0;
            }

            .model-info {
              flex: 1;
              min-width: 0;

              .model-name {
                margin: 0 0 16px 0;
                font-size: 18px;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.85);
                line-height: 1.3;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .model-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                .category-tag {
                  border: none;
                  border-radius: 4px;
                  font-size: 12px;
                  padding: 2px 8px;
                  margin: 0;
                }
              }
            }
          }

          .model-description {
            width: fit-content;
            height: 42px;
            color: rgba(0, 0, 0, 0.45);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .model-stats {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            gap: 12px;
            height: 36px;

            .stats-left {
              display: flex;
              gap: 12px;

              .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
              }
            }

            .stats-right {
              color: rgba(0, 0, 0, 0.25);
              font-size: 14px;
            }
            .ant-btn {
              flex: 1;
              height: 36px;
            }
            .ant-btn:first-child {
              color: rgba(0, 0, 0, 0.85);
              border: 1px solid #e6eaf2;
              background-color: #f5f7fa;
              text-shadow: none;
              box-shadow: none;
            }
            .inference-btn {
              border-color: #ff8d2f;
              background-color: #ff8d2f;
              // 相近的颜色
              &:hover {
                background-color: #ffa94d;
                border-color: #ffa94d;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .marketplace-content {
      .models-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      }
    }
  }

  @media (max-width: 768px) {
    .marketplace-content {
      .models-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}

.marketplace-toolbar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 48px;
  margin: 0 auto 0 auto;
  z-index: 1;
  &.hide {
    margin-top: 80px;
  }
  .favorites-section {
    width: 160px;
    display: flex;
    align-items: center;
    gap: 4px;
    .favorites-icon {
      color: #ff9500;
      font-size: 16px;
    }
    span {
      font-size: 14px;
      color: #5f6368;
    }
    .favorites-count {
      margin-left: 8px;
      color: #5f6368;
      font-size: 14px;
      font-weight: 400;
    }
  }
  .recommend-select-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    .favorites-count {
      color: #5f6368;
      font-size: 14px;
      font-weight: 400;
      .favorites-count-number {
        color: var(--ant-primary-color);
      }
    }
  }
}
