import { AxiosResponse } from 'axios';
import axios, { AxiosResFormat } from '.';
import { IADUGTemplateInfo, IAppHeaderItem, IAppMenuItem, ICustomDialog, IPipelineAdd } from './interface/kubeflowInterface';
import { ITabsModalData } from './interface/tabsModalInterface';

export const getAppMenu = (): Promise<AxiosResponse<IAppMenuItem[]>> => {
  return axios.get('/myapp/menu');
};

export const getAppHeaderConfig = (): Promise<AxiosResponse<IAppHeaderItem[]>> => {
  return axios.get('/myapp/navbar_right');
};

export const userLogout = (): Promise<AxiosResponse<IAppMenuItem[]>> => {
  return axios.get('/logout');
};

export const getADUGTemplateApiInfo = (url?: string, id?: string): Promise<AxiosResponse<IADUGTemplateInfo>> => {
  return axios.get(`${url || ''}_info`, {
    params: {
      id,
    },
  });
};

export const getCustomDialog = (url: string, signal: AbortSignal): Promise<AxiosResponse<ICustomDialog>> => {
  return axios.get(`/myapp/feature/check?url=${url}`, { signal });
};

export const getADUGTemplateList = (url?: string, params?: any): AxiosResFormat<any> => {
  return axios.get(url || '', { params });
};

export const getData = (url?: string, params?: any): AxiosResFormat<any> => {
  return axios.get(url || '', { params });
};

export const postData = (url?: string, params?: any): AxiosResFormat<any> => {
  return axios.post(url || '', params);
};

export const putData = (url?: string, params?: {}): AxiosResFormat<any> => {
  return axios.put(url || '', params);
};

export const getADUGTemplateDetail = (url: string, form_data?: any): AxiosResFormat<any> => {
  const formData = form_data || { str_related: 1 };
  return axios.get(`${url}`, {
    params: {
      form_data: JSON.stringify(formData),
    },
  });
};

export const actionADUGTemplateAdd = (url?: string, params?: {}): AxiosResFormat<any> => {
  return axios.post(url || '', params);
};

export const actionADUGTemplateUpdate = (url?: string, params?: {}): AxiosResFormat<any> => {
  return axios.put(url || '', params);
};

export const actionADUGTemplateDelete = (url?: string, params?: {}): AxiosResFormat<any> => {
  return axios.delete(url || '', { params });
};

export const actionADUGTemplateSingle = (url?: string): AxiosResFormat<any> => {
  return axios.get(url || '');
};

export const actionADUGTemplateMuliple = (url?: string, params?: { ids: any[] }): AxiosResFormat<any> => {
  return axios.post(url || '', params);
};

export const actionADUGTemplateDownData = (url: string): AxiosResFormat<any> => {
  return axios.get(url);
};

export const actionADUGTemplateRetryInfo = (url: string, params: any): Promise<AxiosResponse<IADUGTemplateInfo>> => {
  return axios.get(url, { params });
};

export const actionADUGTemplateChartOption = (url?: string, params?: {}): Promise<any> => {
  return axios.get(url || '', { params });
};

export const actionADUGTemplateFavorite = (url?: string, params?: {}): AxiosResFormat<any> => {
  return axios.post(url || '', params);
};

export const actionADUGTemplateCancelFavorite = (url?: string, params?: {}): AxiosResFormat<any> => {
  return axios.delete(url || '', { params });
};

export const actionTabsModalInfo = (url: string): AxiosResFormat<ITabsModalData> => {
  return axios.get(url);
};

export const actionTabsModal = (method: 'get' | 'post' | 'delete', url?: string, params?: {}): Promise<any> => {
  return axios[method](url || '', { params });
};

// 新增流水线
export const pipeline_modelview_add = (data: any): Promise<any> => {
  return axios.post('/pipeline_modelview/api/', data);
};
// 创建aihub
export const pipeline_modelview_aihub_create = (data: any): Promise<any> => {
  return axios.post('/pipeline_modelview/api/aihub_create', data);
};
// 流水线编辑提交
export const pipeline_modelview_edit = (pipelineId: number | string, data: any): Promise<any> => {
  return axios.put(`/pipeline_modelview/api/${pipelineId}`, data);
};
// 登陆
export const userLogin = (data: { username: string; password: string }): Promise<any> => {
  return axios.post('/api/v1/login', data);
};
// 获取aihub文档
export const getAihubDocs = (): Promise<any> => {
  return axios.get('/aihub/api/docs');
};
// 获取所有 Aihub 数据，支持搜索
export const getAihubApiList = (params: any): Promise<any> => {
  return axios.get('/aihub/api/', { params });
};
// 根据ID获取单个AI模型详细信息
export const getAihubApiById = (id: string): Promise<any> => {
  return axios.get(`/aihub/api/${id}`);
};
// 获取tag列表
export const getTagList = (): Promise<any> => {
  return axios.get('/tag/api/');
};
// 获取project文档
export const getProjectDocs = (): Promise<any> => {
  return axios.get('/project_management/api/docs');
};
// 获取项目管理列表，支持筛选、分页和排序
export const getProjectList = (params: any): Promise<any> => {
  return axios.get('/project_management/api/', { params });
};
// 更新项目管理记录
export const updateProject = (id: number, data: any): Promise<any> => {
  return axios.put(`/project_management/api/${id}`, data);
};
// 删除项目管理记录，仅允许创建人删除
export const deleteProject = (id: number): Promise<any> => {
  return axios.delete(`/project_management/api/${id}`);
};

// 获取dataset文档
export const getDatasetDocs = (): Promise<any> => {
  return axios.get('/dataset/api/docs');
};
//获取数据集列表，支持通过名称、中文名称、描述和版本进行筛选
export const getDatasetList = (params: any): Promise<any> => {
  return axios.get('/dataset/api/', { params });
};
//获取单个数据集的详细信息
export const getDatasetById = (id: number): Promise<any> => {
  return axios.get(`/dataset/api/${id}`);
};
//创建新的数据集
export const createDataset = (data: any): Promise<any> => {
  return axios.post('/dataset/api/', data);
};
// 更新指定数据集的信息
export const updateDataset = (id: number, data: any): Promise<any> => {
  return axios.put(`/dataset/api/${id}`, data);
};
// 删除指定数据集
export const deleteDataset = (id: number): Promise<any> => {
  return axios.delete(`/dataset/api/${id}`);
};
// 上传文件到服务器(blob)
export const uploadFile = (data: any): Promise<any> => {
  return axios.post('/dataset/api/upload_file', data);
};
// 获取推理服务列表
export const getInferenceServiceList = (params: any): Promise<any> => {
  return axios.get('/inferenceservice_modelview/api/', { params });
};
// 获取推理服务模型视图信息
export const getInferenceServiceModelViewInfo = (): Promise<any> => {
  return axios.get('/inferenceservice_modelview/api/_info');
};
// 创建推理服务模型视图记录
export const createInferenceServiceModelView = (data: any): Promise<any> => {
  return axios.post('/inferenceservice_modelview/api/', data);
};
// 详情
export const getInferenceServiceModelViewDetail = (id: string, params: any): Promise<any> => {
  return axios.get(`/inferenceservice_modelview/api/${id}`, { params });
};
// 修改
export const updateInferenceServiceModelView = (id: string, data: any): Promise<any> => {
  return axios.put(`/inferenceservice_modelview/api/${id}`, data);
};
// 删除
export const deleteInferenceServiceModelView = (id: string): Promise<any> => {
  return axios.delete(`/inferenceservice_modelview/api/${id}`);
};
// 批量复制
export const copyInferenceServiceModelView = (data: any): Promise<any> => {
  return axios.post('/inferenceservice_modelview/api/multi_action/copy', data);
};
