import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Space, Button, message } from 'antd';
import './EditDatasetModal.less';
import { getTagList, updateDataset } from '../../../../../api/kubeflowApi';

interface DatasetItem {
  id: number;
  name: string;
  chinese_name: string;
  description: string;
  tags: any[];
  version: string;
}

interface EditDatasetModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  dataset?: DatasetItem;
}

const EditDatasetModal: React.FC<EditDatasetModalProps> = ({ visible, onCancel, onOk, dataset }) => {
  const [form] = Form.useForm();
  const [tags, setTags] = useState<any[]>([]);

  useEffect(() => {
    if (visible && dataset) {
      form.setFieldsValue({
        name: dataset.name,
        chinese_name: dataset.chinese_name,
        description: dataset.description,
        tags: dataset.tags.map((tag) => tag.id),
        version: dataset.version,
      });
    }
  }, [visible, dataset, form]);

  useEffect(() => {
    if (visible) {
      getTagList().then((res) => {
        const { status, data } = res;
        if (status === 200) {
          setTags(data.result.data);
        }
      });
    }
  }, [visible]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      values.tags = values.tags.join(',');
      updateDataset(dataset?.id || 0, values).then((res) => {
        const { status } = res;
        if (status === 200) {
          message.success('修改成功');
          onCancel();
        }
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="修改数据集"
      open={visible}
      onCancel={handleCancel}
      width="50%"
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          确定
        </Button>,
      ]}
      className="edit-dataset-modal common-modal" // 应用自定义样式
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: '',
          chinese_name: '',
          description: '',
          tags: [],
          version: '',
        }}
      >
        <Form.Item name="name" label="名称" rules={[{ required: true, message: '请输入名称' }]}>
          <Input placeholder="请输入名称" />
        </Form.Item>

        <Form.Item name="chinese_name" label="中文名" rules={[{ required: true, message: '请输入中文名' }]}>
          <Input placeholder="请输入中文名" />
        </Form.Item>

        <Form.Item name="description" label="描述" rules={[{ required: true, message: '请输入描述' }]}>
          <Input.TextArea rows={4} placeholder="请输入描述" />
        </Form.Item>

        <Form.Item name="tags" label="标签" rules={[{ required: true, message: '请选择标签' }]}>
          <Select
            mode="multiple"
            placeholder="请选择标签"
            options={tags.map((tag) => ({ label: tag.name, value: tag.id }))}
            onChange={(value) => form.setFieldsValue({ tags: value })}
          />
        </Form.Item>

        <Form.Item name="version" label="版本" rules={[{ required: true, message: '请输入版本' }]}>
          <Input placeholder="请输入版本" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditDatasetModal;
