.guide-cover {
  // display: none;
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.3);
  // opacity: .75;
  // filter: alpha(opacity=75);
  z-index: 99;
  /* 过渡效果 */
  // transition: all .3s;
  /* 边缘闪动问题fix */
  // box-shadow: 0 0 0 100px #000;
  box-sizing: content-box !important;
}

.guide-cover::before {
  content: '';
  width: 100%;
  height: 100%;
  // border-radius: 50%;
  border-width: 600px;
  border-style: solid;
  border-color: transparent;
  position: absolute;
  left: -600px;
  top: -600px;
  box-sizing: content-box !important;
  box-shadow: inset 0 0 5px 2px rgba(0, 0, 0, 0.3);
}

/* IE7, IE8 img */
.guide-cover > img {
  width: 100%;
  height: 100%;
}

.guide-tip-container {
  position: absolute;
  // top: calc(100% + 15px);
  width: 100%;
  // text-align: center;
}

.guide-tip {
  position: relative;
  padding: 12px 16px 12px 16px;
  background: #fff;
  border: 1px solid #fff;
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.3);
  min-width: 300px;
  // position: absolute;
  // right: 0;

  // &:after {
  //     content: '';
  //     position: absolute;
  //     margin: 0 auto;
  //     left: 0;
  //     right: 0;
  //     bottom: 100%;
  //     display: block;
  //     width: 0;
  //     height: 0;
  //     border-style: solid;
  //     border-width: 10px;
  //     border-color: transparent transparent #272A33 transparent;
  // }
}

.guide-arrow {
  display: block;
  position: absolute;
  margin: 0 auto;
  left: 0;
  right: 0;
  bottom: 100%;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px;
  border-color: transparent transparent #fff transparent;
  box-sizing: content-box !important;
}
