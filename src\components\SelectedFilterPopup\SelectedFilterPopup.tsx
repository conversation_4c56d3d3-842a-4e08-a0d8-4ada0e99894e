import React from 'react';
import { Tag, Popover, Image } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import './SelectedFilterPopup.less';
import image_1749806589896_ncbnhv from '../../images/image_1749806589896_ncbnhv.svg';

interface SelectedTag {
  key: string;
  label: string;
  removable: boolean;
}

interface SelectedFilterPopupProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  selectedTags: SelectedTag[];
  onRemoveTag: (key: string) => void;
  onClearAll: () => void;
  triggerNode: React.ReactNode;
}

const SelectedFilterPopup: React.FC<SelectedFilterPopupProps> = ({
  visible,
  onVisibleChange,
  selectedTags,
  onRemoveTag,
  onClearAll,
  triggerNode,
}) => {
  return (
    <Popover
      content={
        <div className="selected-filter-popup-content">
          <div className="selected-filter-popup-header">
            <span className="selected-filter-popup-header-title">已筛选条件</span>
            <Image src={image_1749806589896_ncbnhv} preview={false} className="selected-filter-popup-header-clear" onClick={onClearAll} />
          </div>
          <div className="selected-filter-popup-tags">
            {selectedTags.length === 0 && <span className="selected-filter-popup-tags-empty">暂无筛选条件</span>}
            {selectedTags.map((tag) => (
              <Tag key={tag.key} closable={tag.removable} onClose={() => onRemoveTag(tag.key)} className="selected-filter-popup-tag">
                {tag.label}
              </Tag>
            ))}
          </div>
        </div>
      }
      title={null}
      trigger="click"
      open={visible}
      onOpenChange={onVisibleChange}
      placement="bottomRight"
    >
      {triggerNode}
    </Popover>
  );
};

export default SelectedFilterPopup;
