'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { Input, Button, Select, Tag, Switch, Collapse, Typography, Image, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import '../common.less';
import DrawerDetailDatabase from '../../../components/DrawerDetailDatabase/DrawerDetailDatabase';
import image_1749625960270_1e8drs from '../../../images/image_1749625960270_1e8drs.svg';
import image_1749625966598_ckpjxz from '../../../images/image_1749625966598_ckpjxz.svg';
import image_1749625972548_1qi5fp from '../../../images/image_1749625972548_1qi5fp.svg';
import image_1749625978675_jzgl86 from '../../../images/image_1749625978675_jzgl86.svg';
import image_1749625984405_mnwt2g from '../../../images/image_1749625984405_mnwt2g.svg';
import image_1749625989491_axcu99 from '../../../images/image_1749625989491_axcu99.svg';
import image_1749625996394_w4xkq7 from '../../../images/image_1749625996394_w4xkq7.svg';
import translateBaidu from '../../../api/translateBaidu';
import SelectedFilterPopup from '../../../components/SelectedFilterPopup/SelectedFilterPopup';
const { Search } = Input;
const { Option } = Select;
const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;

// 定义数据接口
interface DatasetItem {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  date: string;
  views: number;
  downloads: number;
  samples: number;
}

// 疾病类型数据
const diseaseTypes = [
  'Melanoma',
  'Lung Cancer',
  'Breast Invasive Carcinoma',
  'Gastroesophageal Cancer',
  'Acute Myeloid Leukemia',
  'Colorectal Cancer',
  'Ovarian Cancer',
  'Multiple Myeloma',
];
// 组织部位数据
const organizationTypes = ['Breast', 'Colorectal', 'Lung', 'Prostate'];

// 数据类型标签
const dataTypeTags = [
  { key: 'Other', label: 'Other', removable: true },
  { key: 'CT', label: 'CT', removable: true },
  { key: 'MR', label: 'MR', removable: true },
  { key: 'MS', label: 'MS', removable: true },
  { key: 'US', label: 'US', removable: true },
  { key: 'PT', label: 'PT', removable: true },
  { key: 'NM', label: 'NM', removable: false },
  { key: 'CR', label: 'CR', removable: false },
];

// 筛选选项配置
const filterOptions = {
  diseases: {
    title: '疾病类型',
    key: 'diseases',
    options: diseaseTypes,
  },
  organization: {
    title: '组织部位',
    key: 'organization',
    options: organizationTypes,
  },
  dataTypes: {
    title: '数据类型',
    key: 'dataTypes',
    options: dataTypeTags.map((tag) => tag.label),
  },
};

// 推荐下拉菜单选项
const recommendItems: MenuProps['items'] = [
  { key: '最新', label: '最新' },
  { key: '最热', label: '最热' },
  { key: '推荐', label: '推荐' },
];

// 模拟数据集数据
const mockDatasets: DatasetItem[] = [
  {
    id: '1',
    title: 'Stagell-Colorectal-CT',
    subtitle: 'Abdominal or pelvic enhanced CT images within 10 days before surgery of 230 patients with sta...',
    description:
      'This dataset includes abdominal or pelvic enhanced CT images within 10 days before surgery of 230 patients with stage II colorectal cancer (CRC). The inclusion criteria were as follows: (i) patients with radical surgery for CRC (complete removal of the original tumor and regional lymphadenectomy); (ii) patients with stage II CRC confirmed by...',
    date: '2025-04-30',
    views: 1286,
    downloads: 389,
    samples: 166,
  },
  {
    id: '2',
    title: 'COLORECTAL-LIVER-METASTASES...',
    subtitle: 'Abdominal or pelvic enhanced CT images within 10 days before surgery of 230 patients with sta...',
    description:
      'This dataset includes abdominal or pelvic enhanced CT images within 10 days before surgery of 230 patients with stage II colorectal cancer (CRC). The inclusion criteria were as follows: (i) patients with radical surgery for CRC (complete removal of the original tumor and regional lymphadenectomy); (ii) patients with stage II CRC confirmed by...',
    date: '2025-04-30',
    views: 1286,
    downloads: 389,
    samples: 166,
  },
  {
    id: '3',
    title: 'Stagell-Colorectal-CT',
    subtitle: 'Abdominal or pelvic enhanced CT images within 10 days before surgery of 230 patients with sta...',
    description:
      'This dataset includes abdominal or pelvic enhanced CT images within 10 days before surgery of 230 patients with stage II colorectal cancer (CRC). The inclusion criteria were as follows: (i) patients with radical surgery for CRC (complete removal of the original tumor and regional lymphadenectomy); (ii) patients with stage II CRC confirmed by...',
    date: '2025-04-30',
    views: 1286,
    downloads: 389,
    samples: 166,
  },
  {
    id: '4',
    title: 'Lung-Cancer-MRI-Segmentation',
    subtitle: 'High-resolution MRI scans of 150 patients with early-stage lung cancer, including tumor segmentation...',
    description:
      'Comprehensive collection of MRI scans from patients diagnosed with early-stage lung cancer. Dataset includes detailed tumor segmentation masks, patient demographics, and clinical outcomes. All scans were performed using standardized protocols across multiple medical centers.',
    date: '2025-04-28',
    views: 2156,
    downloads: 892,
    samples: 150,
  },
  {
    id: '5',
    title: 'Breast-Cancer-Mammography',
    subtitle: 'Digital mammography images from 300 patients with biopsy-confirmed breast cancer...',
    description:
      'Large-scale collection of digital mammography images with corresponding biopsy results. Dataset includes both benign and malignant cases, with detailed annotations of tumor locations and characteristics. All images were collected using standardized protocols.',
    date: '2025-04-25',
    views: 3456,
    downloads: 1234,
    samples: 300,
  },
  {
    id: '6',
    title: 'Brain-Tumor-MRI-3D',
    subtitle: '3D MRI scans of brain tumors from 200 patients, including multiple sequences...',
    description:
      'Comprehensive 3D MRI dataset featuring multiple sequences (T1, T2, FLAIR, DWI) from patients with various types of brain tumors. Includes detailed segmentation masks and clinical annotations.',
    date: '2025-04-20',
    views: 1890,
    downloads: 756,
    samples: 200,
  },
  {
    id: '7',
    title: 'Liver-Cancer-CT-Contrast',
    subtitle: 'Contrast-enhanced CT scans of liver cancer patients with follow-up imaging...',
    description:
      'Longitudinal collection of contrast-enhanced CT scans from patients with hepatocellular carcinoma. Includes pre-treatment, post-treatment, and follow-up scans with detailed clinical annotations.',
    date: '2025-04-15',
    views: 1678,
    downloads: 543,
    samples: 180,
  },
  {
    id: '8',
    title: 'Prostate-Cancer-MRI',
    subtitle: 'Multi-parametric MRI scans of prostate cancer patients with biopsy correlation...',
    description:
      'Collection of multi-parametric MRI scans with corresponding biopsy results. Includes T2-weighted, diffusion-weighted, and dynamic contrast-enhanced sequences.',
    date: '2025-04-10',
    views: 2345,
    downloads: 987,
    samples: 250,
  },
];

const TCIAPlatform: React.FC = () => {
  // 状态管理
  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedDiseases, setSelectedDiseases] = useState<string[]>(['Colorectal Cancer']);
  const [dataTypes, setDataTypes] = useState<typeof dataTypeTags>(dataTypeTags);
  const [isAdvancedMode, setIsAdvancedMode] = useState<boolean>(true);
  const [isTranslateMode, setIsTranslateMode] = useState<boolean>(false);
  const [selectedDataset, setSelectedDataset] = useState<DatasetItem | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [showHeader, setShowHeader] = useState(false);
  const [translatedDatasets, setTranslatedDatasets] = useState<DatasetItem[]>([]);
  const [isTranslating, setIsTranslating] = useState(false);
  const [translateError, setTranslateError] = useState<string>('');
  const [selectedFilterPopupVisible, setSelectedFilterPopupVisible] = useState(false);

  // 监听翻译模式变化
  useEffect(() => {
    const translateDatasets = async () => {
      if (isTranslateMode) {
        setIsTranslating(true);
        setTranslateError('');
        try {
          // 收集所有需要翻译的文本
          const textsToTranslate = mockDatasets.flatMap((dataset) => [dataset.title, dataset.subtitle, dataset.description]);

          // 一次性翻译所有文本
          const translatedTexts = await translateBaidu(textsToTranslate.join('\n'));

          // 将翻译结果分割回原来的数组
          const translatedArray = Array.isArray(translatedTexts)
            ? translatedTexts.map((item: { src: string; dst: string }) => item.dst)
            : [];

          // 重新组装数据集
          const translated = mockDatasets.map((dataset, index) => {
            const baseIndex = index * 3;
            return {
              ...dataset,
              title: translatedArray[baseIndex],
              subtitle: translatedArray[baseIndex + 1],
              description: translatedArray[baseIndex + 2],
            };
          });

          setTranslatedDatasets(translated);
        } catch (error) {
          console.error('翻译出错:', error);
          setTranslateError(error instanceof Error ? error.message : '翻译失败，请稍后重试');
          setIsTranslateMode(false);
        } finally {
          setIsTranslating(false);
        }
      } else {
        setTranslatedDatasets([]);
        setTranslateError('');
      }
    };

    translateDatasets();
  }, [isTranslateMode]);

  // 添加滚动监听
  useEffect(() => {
    const container = document.querySelector('.dataset-list');
    const handleScroll = () => {
      if (container) {
        const scrollTop = container.scrollTop;
        setShowHeader(scrollTop > 200); // 当滚动超过200px时显示header
      }
    };

    container?.addEventListener('scroll', handleScroll);
    return () => container?.removeEventListener('scroll', handleScroll);
  }, []);

  // 搜索处理函数
  const handleSearch = (value: string) => {
    console.log('搜索内容:', value);
  };

  // 疾病类型选择处理
  const handleDiseaseSelect = (disease: string) => {
    if (selectedDiseases.includes(disease)) {
      setSelectedDiseases(selectedDiseases.filter((item) => item !== disease));
    } else {
      setSelectedDiseases([...selectedDiseases, disease]);
    }
  };

  // 移除数据类型标签
  const handleRemoveDataType = (key: string) => {
    setDataTypes(dataTypes.filter((item) => item.key !== key));
  };
  // 移除数据类型标签
  const handleRemoveTag = (key: string) => {
    setSelectedDiseases(selectedDiseases.filter((disease) => disease !== key));
  };

  const handleClearAll = () => {
    setSelectedDiseases([]);
  };

  // 处理数据集点击
  const handleDatasetClick = (item: DatasetItem) => {
    setSelectedDataset(item);
    setDrawerVisible(true);
  };

  // 修改渲染数据集项目的函数
  const renderDatasetItem = (item: DatasetItem) => {
    const displayItem = isTranslateMode ? translatedDatasets.find((t) => t.id === item.id) || item : item;

    return (
      <div key={item.id} className="dataset-item" onClick={() => handleDatasetClick(item)}>
        <div className="dataset-header">
          <Title level={4} className="dataset-title" title={displayItem.title}>
            {isTranslating ? '翻译中...' : displayItem.title}
          </Title>
        </div>
        <div className="dataset-description">
          <Paragraph className="description-text" title={displayItem.description}>
            {isTranslating ? '翻译中...' : displayItem.description}
          </Paragraph>
        </div>
        <div className="dataset-meta">
          <div className="meta-left">
            <span className="meta-item">
              <Image src={image_1749625978675_jzgl86} preview={false} /> {displayItem.date}
            </span>
            <span className="meta-item">
              <Image src={image_1749625972548_1qi5fp} preview={false} /> {displayItem.views}
            </span>
            <span className="meta-item">
              <Image src={image_1749625966598_ckpjxz} preview={false} /> {displayItem.downloads}
            </span>
            <span className="meta-item">
              <Image src={image_1749625960270_1e8drs} preview={false} /> {displayItem.samples}
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="database-platform">
      {/* 显示错误消息 */}
      {translateError && (
        <div
          className="translate-error"
          style={{
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            backgroundColor: '#fff2f0',
            border: '1px solid #ffccc7',
            padding: '8px 16px',
            borderRadius: '4px',
            zIndex: 1000,
          }}
        >
          {translateError}
        </div>
      )}

      {/* 原始头部区域 */}
      <header className={`marketplace-header-bg ${showHeader ? 'hide' : ''}`}>
        <div className="marketplace-header-content">
          <h1 className="marketplace-title">TCIA</h1>
          <div className="marketplace-desc">开放共享多模态影像资源，赋能精准诊断与AI驱动研究​</div>
          <div className="marketplace-search-bar">
            <Input
              className="marketplace-search-input"
              placeholder="搜索关键词，如：分割、膜腺、脑部"
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onPressEnter={() => {
                /* 触发搜索逻辑 */
              }}
              suffix={
                <Button
                  type="primary"
                  size="large"
                  onClick={() => {
                    /* 触发搜索逻辑 */
                  }}
                >
                  搜索
                </Button>
              }
            />
          </div>
        </div>
      </header>

      {/* 固定头部区域 */}
      <header className={`marketplace-header ${showHeader ? 'show' : ''}`}>
        <div className="header-left">
          <h1 className="site-title">TCIA</h1>
        </div>
        <div className="header-right">
          <Input
            className="search-input"
            placeholder="请输入关键词"
            prefix={<SearchOutlined />}
            suffix={<span className="search-shortcut">⌘ K</span>}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </div>
      </header>

      {/* 主体内容区域 */}
      <div className={`main-content ${showHeader ? 'hide' : ''}`}>
        {/* 左侧筛选栏 */}
        {!showHeader && (
          <div className="sidebar">
            <Collapse defaultActiveKey={Object.keys(filterOptions)} ghost className="filter-collapse">
              {Object.values(filterOptions).map(({ title, key, options }) => (
                <Panel header={title} key={key} className="filter-panel">
                  <div className="disease-list">
                    {options.map((option) => (
                      <div
                        key={option}
                        className={`disease-item ${selectedDiseases.includes(option) ? 'selected' : ''}`}
                        onClick={() => handleDiseaseSelect(option)}
                      >
                        {option}
                        {selectedDiseases.includes(option) && <Image src={image_1749625996394_w4xkq7} preview={false} />}
                      </div>
                    ))}
                  </div>
                </Panel>
              ))}
            </Collapse>
          </div>
        )}

        {/* 右侧内容区域 */}
        <div className="content-area">
          {/* 结果统计和控制栏 */}
          <div className="result-header">
            <div className="result-info">
              <Text className="result-count">找到 1299 个大数据</Text>
            </div>
            <div className="result-controls">
              <div className="control-item">
                <Image src={image_1749625989491_axcu99} preview={false} />
                <Text>筛选</Text>
                <SelectedFilterPopup
                  visible={selectedFilterPopupVisible}
                  onVisibleChange={setSelectedFilterPopupVisible}
                  selectedTags={selectedDiseases.map((disease) => ({
                    key: disease,
                    label: disease,
                    removable: true,
                  }))}
                  onRemoveTag={handleRemoveTag}
                  onClearAll={handleClearAll}
                  triggerNode={
                    <Switch
                      checked={isAdvancedMode}
                      onChange={(checked) => {
                        setIsAdvancedMode(checked);
                        if (checked) {
                          setSelectedFilterPopupVisible(true);
                        }
                      }}
                      size="small"
                    />
                  }
                />
              </div>
              <div className="control-item">
                <Image src={image_1749625984405_mnwt2g} preview={false} />
                <Text>翻译</Text>
                <Switch checked={isTranslateMode} onChange={setIsTranslateMode} size="small" />
              </div>
              <div className="control-item">
                <Dropdown menu={{ items: recommendItems }} trigger={['click']} placement="bottomRight">
                  <Button className="filter-button" type="text">
                    推荐 <DownOutlined />
                  </Button>
                </Dropdown>
              </div>
            </div>
          </div>

          {/* 数据集列表 */}
          <div className={`dataset-list ${showHeader ? 'hide' : ''}`}>
            {(isTranslateMode ? translatedDatasets : mockDatasets).map(renderDatasetItem)}
          </div>
        </div>
      </div>

      <DrawerDetailDatabase visible={drawerVisible} onClose={() => setDrawerVisible(false)} />
    </div>
  );
};

export default TCIAPlatform;
