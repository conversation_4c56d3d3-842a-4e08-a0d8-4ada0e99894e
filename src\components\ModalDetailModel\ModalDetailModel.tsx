import React, { useEffect, useState } from 'react';
import { Modal, Button, Tag, message, Image } from 'antd';
import { HeartOutlined, ShareAltOutlined, ArrowRightOutlined } from '@ant-design/icons';
import './ModalDetailModel.less';
import image_1749610078732_5tzde1 from '../../images/image_1749610078732_5tzde1.svg';
import image_1749610091638_ilf67y from '../../images/image_1749610091638_ilf67y.svg';
import image_1749549898487_81scnv from '../../images/image_1749549898487_81scnv.svg';
import Cookies from 'js-cookie';
import { pipeline_modelview_add, getAihubApiById } from '../../api/kubeflowApi';
import { handleModelTrain } from '../../util';

// 组件属性接口定义
interface MedicalModalProps {
  model?: any;
  /** 弹窗是否可见 */
  visible: boolean;
  /** 弹窗标题 */
  title?: string;
  /** 确认按钮文字 */
  okText?: string;
  /** 取消按钮文字 */
  cancelText?: string;
  /** 点击确认回调 */
  onOk?: () => void;
  /** 点击取消回调 */
  onCancel?: () => void;
  /** 自定义内容 */
  content?: React.ReactNode;
  /** 部署处理函数 */
  onDeploy?: (model: any) => void;
}
// 前往使用
const handleTrain = (model: any) => {
  handleModelTrain(model);
};
// 前往推理
const handleDeploy = (model: any, onDeploy?: (model: any) => void) => {
  if (onDeploy) {
    onDeploy(model);
  } else {
    message.warning('此功能暂未开发');
  }
};

/**
 * 医学影像弹窗组件
 * 用于展示医学影像分析工具的详细信息
 */
const MedicalModal: React.FC<MedicalModalProps> = ({
  visible,
  title = 'MED SAM2.0 多器官分割',
  okText = '确定',
  cancelText = '取消',
  onOk,
  onCancel,
  content,
  model,
  onDeploy,
}) => {
  const [modelInfo, setModelInfo] = useState<any>(null);
  // 递归渲染文件树
  const renderFileTree = (nodes: string | any[], level = 0) => {
    let arr: any[] = [];
    if (typeof nodes === 'string') {
      try {
        const parsed = JSON.parse(nodes);
        arr = Array.isArray(parsed) ? parsed : [parsed];
      } catch (e) {
        return null; // 解析失败直接返回
      }
    } else if (Array.isArray(nodes)) {
      arr = nodes;
    } else if (typeof nodes === 'object' && nodes !== null) {
      arr = [nodes];
    } else {
      return null;
    }

    return arr.map((node: any, idx: number) => (
      <div key={node.path || node.name || idx} className={`medical-modal__file-item medical-modal__file-item--level-${level}`}>
        <div className="medical-modal__file-name">
          {node.type === 'directory' || node.children ? '📁' : '📄'} {node.name}
        </div>
        {node.children && node.children.length > 0 && (
          <div className="medical-modal__file-children">{renderFileTree(node.children, level + 1)}</div>
        )}
      </div>
    ));
  };
  useEffect(() => {
    if (model && visible) {
      getAihubApiById(model.id).then((res) => {
        const { status, data } = res;
        if (status === 200) {
          data.result.iconBg =
            '#' +
            Math.floor(Math.random() * 16777215)
              .toString(16)
              .padStart(6, '0'); // 随机背景色
          data.result.icon =
            data.result.icon && data.result.icon.startsWith('http') && /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(data.result.icon)
              ? data.result.icon
              : data.result.name.charAt(0); // 只有当 item.icon 是有效的HTTP图片URL时才使用，否则使用名称首字母
          setModelInfo(data.result);
        }
      });
    }
  }, [model, visible]);

  return (
    <Modal title={null} open={visible} onOk={onOk} onCancel={onCancel} footer={null} width="40%" className="medical-modal" destroyOnClose>
      <div className="medical-modal__container">
        {/* 右侧信息区域 */}
        <div className="medical-modal__info-section">
          <div className="medical-modal__item">
            <div className="medical-modal__name">
              {/* 判断是否为图片URL */}
              {modelInfo?.icon && modelInfo?.icon.length > 1 ? (
                <div className="medical-modal__icon">
                  <img
                    src={modelInfo?.icon}
                    alt={modelInfo?.name}
                    style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '8px' }}
                  />
                </div>
              ) : (
                // 如果不是图片URL，则显示文字
                <div className="medical-modal__icon" style={{ backgroundColor: modelInfo?.iconBg }}>
                  {modelInfo?.icon}
                </div>
              )}
              {/* 标题区域 */}
              <div className="medical-modal__header">
                <h2 className="medical-modal__title">{modelInfo?.name}</h2>
                {/* 标签区域 */}
                <div className="medical-modal__tags">
                  {modelInfo?.tags.map((tag: any) => (
                    <Tag className="medical-modal__tag" key={tag.id}>
                      {tag.name}
                    </Tag>
                  ))}
                </div>
              </div>
            </div>
            {/* 操作按钮区域 */}
            <div className="medical-modal__actions">
              {modelInfo?.type.includes('train') && (
                <Button type="primary" size="large" className="medical-modal__use-btn" onClick={() => handleTrain(modelInfo)}>
                  <img src={image_1749610091638_ilf67y} alt="" />
                  <span>前往使用</span>
                </Button>
              )}
              {modelInfo?.type.includes('inference') && (
                <Button
                  type="primary"
                  color="#000000"
                  size="large"
                  className="medical-modal__inference-btn"
                  onClick={() => handleDeploy(modelInfo, onDeploy)}
                >
                  <img src={image_1749610091638_ilf67y} alt="" />
                  <span>前往推理</span>
                </Button>
              )}
            </div>
          </div>

          <div className="medical-modal__item">
            {/* 描述区域 */}
            <div className="medical-modal__description">
              <p className="medical-modal__desc-text">{modelInfo?.description}</p>
            </div>
            {/* 操作按钮区域 */}
            <div className="medical-modal__stats">
              <div className="medical-modal__stat-item">
                <img src={image_1749549898487_81scnv} alt="" />
                <span>{modelInfo?.view || 0}</span>
              </div>
              <div className="medical-modal__stat-item">
                <img src={image_1749610078732_5tzde1} alt="" />
                <span>{modelInfo?.hot || 0}</span>
              </div>
            </div>
          </div>

          {/* 文件结构区域 */}
          <div className="medical-modal__file-section">
            <h3 className="medical-modal__section-title">上传结构</h3>
            {typeof modelInfo?.dataset === 'string' && modelInfo?.dataset.trim() !== '' && modelInfo?.dataset.trim() !== '{}' ? (
              <div className="medical-modal__file-tree">{renderFileTree(modelInfo?.dataset)}</div>
            ) : (
              <div className="medical-modal__file-tree">暂无数据</div>
            )}
          </div>

          {/* 自定义内容 */}
          {content && <div className="medical-modal__custom-content">{content}</div>}
        </div>
      </div>
    </Modal>
  );
};

export default MedicalModal;
