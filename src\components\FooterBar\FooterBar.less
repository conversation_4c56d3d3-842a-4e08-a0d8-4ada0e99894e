.footer-bar {
  position: relative;
  width: 100%;
  background: #f4f8fc;
  border-top: 1px solid #e5e8ec;
  z-index: 1;
}
.footer-bar__container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 48px 0 32px 0;
}
.footer-bar__col {
  flex: 1;
  min-width: 160px;
  color: #7a869a;
  font-size: 16px;
  line-height: 2.2;
}
.footer-bar__logo-col {
  flex: 1.2;
  display: flex;
  align-items: flex-start;
}
.footer-bar__logo {
  width: 144px;
  height: 25.089px;
  margin-top: 8px;
}
.footer-bar__col-title {
  color: #222;
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 12px;
}
.footer-bar__item {
  cursor: pointer;
  transition: color 0.2s;
}
.footer-bar__item:hover {
  color: #1890ff;
}
@media (max-width: 900px) {
  .footer-bar__container {
    flex-direction: column;
    padding: 32px 16px 24px 16px;
  }
  .footer-bar__col {
    min-width: unset;
    margin-bottom: 24px;
  }
  .footer-bar__logo-col {
    justify-content: flex-start;
    margin-bottom: 16px;
  }
}

.footer-beian {
  padding: 20px 0;
  // 灰色线条
  border-top: 1px solid #e5e8ec;
  text-align: center;

  a {
    color: #7a869a;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.2s;

    &:hover {
      color: #1890ff;
    }
  }
}
