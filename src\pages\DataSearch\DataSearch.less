.datasearch-container {
  .ant-tabs-tabpane {
    height: 100%;
  }
}

.tag-result {
  padding: 1px 8px 1px 16px;
  display: inline-block;
  position: relative;
  left: 0;

  &:after {
    content: '';
    display: inline-block;
    position: absolute;
    left: 100%;
    top: 0;
    width: 0;
    height: 0;
    border-width: 12px;
    border-style: solid;
    border-color: transparent transparent transparent ~'var(--ant-primary-color)';
  }
}

.site-collapse-custom-collapse {
  border: none;
  background-color: #fff;

  .site-collapse-custom-panel {
    margin-bottom: 24px;
    overflow: hidden;
    background: #f7f7f7;
    border: none;
    border-radius: 2px;
  }

  .ant-collapse-content {
    border-top: none;
  }

  .ant-collapse-item-active {
    border: 1px #f7f7f7 solid;
  }

  .status-success {
    border: 1px #03b266 solid;
  }

  .status-failure {
    border: 1px #ff4444 solid;
  }

  .status-running {
    // background: repeating-linear-gradient(
    //     135deg,
    //     transparent,
    //     transparent 40px,
    //     #1672fa 40px,
    //     #1672fa 80px
    // );
    // overflow: hidden;
    // animation: move 1s infinite linear;
  }

  .c-failure {
    color: #ff4444;
  }

  .c-success {
    color: #03b266;
  }
}

.codeedit-mark {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

// @keyframes move {
//     from {
//       background-position: -10px;
//     }
//     to {
//       background-position: -120px;
//     }
//   }
