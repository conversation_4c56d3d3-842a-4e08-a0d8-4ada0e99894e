import React, { useState } from 'react';
import { Modal, Input, Button, Upload, message, Image, Space } from 'antd';
import { UploadOutlined, FolderFilled, CloseOutlined } from '@ant-design/icons';
import image_1749722936349_1k6dn2 from '../../../../images/image_1749722936349_1k6dn2.svg';
import image_1749718147814_89muqh from '../../../../images/image_1749718147814_89muqh.svg';

interface UserFeedbackModalProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (values: { content: string; files: any[] }) => void;
}

const UserFeedbackModal: React.FC<UserFeedbackModalProps> = ({ open, onCancel, onSubmit }) => {
  const [content, setContent] = useState('');
  const [fileList, setFileList] = useState<any[]>([]);
  const [submitting, setSubmitting] = useState(false);

  const handleUploadChange = ({ file, fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  const handleRemove = (file: any) => {
    setFileList(fileList.filter((f) => f.uid !== file.uid));
  };

  const handleSubmit = () => {
    if (!content.trim()) {
      message.warning('请输入您的疑问或建议');
      return;
    }
    setSubmitting(true);
    onSubmit({ content, files: fileList });
    setSubmitting(false);
    setContent('');
    setFileList([]);
  };

  return (
    <Modal
      open={open}
      title="用户反馈"
      onCancel={onCancel}
      centered
      width={600}
      className="common-modal"
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          提交
        </Button>,
      ]}
    >
      <Input.TextArea rows={5} placeholder="请输入您的疑问或建议" value={content} onChange={(e) => setContent(e.target.value)} />
      <Upload
        fileList={fileList}
        beforeUpload={() => false}
        onChange={handleUploadChange}
        onRemove={handleRemove}
        multiple
        showUploadList={false}
      >
        <Button style={{ width: '148px' }}>
          <Space size={4}>
            <Image src={image_1749718147814_89muqh} preview={false} />
            <span style={{ fontSize: '16px', color: 'rgba(0, 0, 0, 0.85)', letterSpacing: 'normal' }}>上传</span>
          </Space>
        </Button>
      </Upload>
      {/* 文件列表 */}
      <div style={{ marginBottom: 32 }}>
        {fileList.map((file) => (
          <div
            key={file.uid}
            style={{ display: 'flex', alignItems: 'center', marginBottom: 8, background: '#f7f8fa', borderRadius: 4, padding: '4px 12px' }}
          >
            <Image src={image_1749722936349_1k6dn2} style={{ width: 16, height: 16, marginRight: 8 }} preview={false} />
            <span style={{ flex: 1, fontSize: 16 }}>{file.name}</span>
            <CloseOutlined style={{ cursor: 'pointer' }} onClick={() => handleRemove(file)} />
          </div>
        ))}
      </div>
    </Modal>
  );
};

export default UserFeedbackModal;
