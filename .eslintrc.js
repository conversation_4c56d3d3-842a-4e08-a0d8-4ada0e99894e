module.exports = {
  // 继承 Create React App 的默认 ESLint 配置
  extends: ['react-app'],

  // 自定义规则配置
  rules: {
    // 关闭未使用变量的警告
    "@typescript-eslint/no-unused-vars": "off",

    // 关闭 React useEffect 的依赖项检查
    "react-hooks/exhaustive-deps": "off",

    // 关闭数组方法必须有返回值的检查
    "array-callback-return": "off",

    // 关闭 TypeScript 中 this 使用的检查
    "@typescript-eslint/no-invalid-this": "off",

    // 关闭 TypeScript 中 this 别名的检查
    "@typescript-eslint/no-this-alias": "off",

    // 关闭强制使用 === 而不是 == 的检查
    "eqeqeq": "off",

    // 关闭禁止使用 eval() 函数的检查
    "no-eval": "off",

    // 关闭 iframe 必须有 title 属性的检查
    "jsx-a11y/iframe-has-title": "off",

    // 关闭不必要的转义字符检查
    "no-useless-escape": "off"
  }
};
