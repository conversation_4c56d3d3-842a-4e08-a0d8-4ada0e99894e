// 抽屉组件样式文件
// 遵循 BEM 命名规范

.drawer-component {
  // Modal 基础样式重置
  .ant-drawer-content {
    padding: 0;
    border-radius: 0;
    height: 100%;
  }

  .ant-drawer-body {
    padding: 0;
    height: 100%;
    overflow: hidden;
  }

  // 主容器
  &__container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  // 内容区域
  &__content {
    flex: 1;
    padding: 16px 40px;

    // 内容头部
    &-header {
      margin-bottom: 20px;
    }

    // 内容标题
    &-title {
      line-height: 1;
      font-size: 24px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      margin: 0 0 8px 0;
    }
  }

  // DOI 信息
  &__doi {
    margin-bottom: 4px;

    &-label {
      color: var(--ant-primary-color);
      margin-right: 4px;
      text-decoration: none;
      cursor: pointer;
    }

    &-link {
      color: var(--ant-primary-color);
      text-decoration: none;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 元数据
  &__meta {
    display: flex;
    gap: 16px;
    color: #666666;
    font-size: 14px;

    &-item {
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 1.1;
    }
  }

  // 表格区域
  &__table-section {
    margin-bottom: 20px;
  }

  // 详细内容
  &__detail {
    height: calc(100vh - 425px);
    padding-right: 10px;
    overflow-y: auto;
    &-text {
      color: rgba(0, 0, 0, 0.65);
      font-size: 16px;
    }
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 底部区域
  &__footer {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    background-color: #f5f7fa;
    flex-shrink: 0;

    &-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  // 翻译文本
  &__translate-text {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
  }
}
