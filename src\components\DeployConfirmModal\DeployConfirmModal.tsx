import React from 'react';
import { Modal, Button } from 'antd';
import './DeployConfirmModal.less';

interface DeployConfirmModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  modelName?: string;
}

const DeployConfirmModal: React.FC<DeployConfirmModalProps> = ({ visible, onCancel, onConfirm, modelName = '模型' }) => {
  return (
    <Modal
      title="提示"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={400}
      className="deploy-confirm-modal common-modal"
      destroyOnClose
    >
      <div className="deploy-confirm-content">
        <p>在开始推理前，需要先完成模型部署，是否现在前往部署？</p>

        <div className="deploy-confirm-actions">
          <Button onClick={onCancel} className="cancel-btn">
            取消
          </Button>
          <Button type="primary" onClick={onConfirm} className="confirm-btn">
            前往部署
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeployConfirmModal;
