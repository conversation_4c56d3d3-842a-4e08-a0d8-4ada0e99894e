import React from 'react';
import { Modal, Form, Input, Button, Image } from 'antd';
import image_1749723592408_tkbxng from '../../../../images/image_1749723592408_tkbxng.svg';

interface AccountSettingModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: {
    nickname?: string;
    email?: string;
    phone?: string;
    avatarUrl?: string;
  };
}

const AccountSettingModal: React.FC<AccountSettingModalProps> = ({ open, onCancel, onOk, initialValues }) => {
  const [form] = Form.useForm();
  React.useEffect(() => {
    if (open) {
      form.setFieldsValue(initialValues);
    }
  }, [open, initialValues, form]);

  return (
    <Modal
      open={open}
      title="账号设置"
      onCancel={onCancel}
      centered
      width={400}
      className="common-modal"
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={onOk}>
          保存
        </Button>,
      ]}
    >
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <Image src={initialValues?.avatarUrl || image_1749723592408_tkbxng} preview={false} style={{ width: 80, height: 80 }} />
      </div>
      <Form form={form} layout="horizontal" onFinish={onOk}>
        <Form.Item
          label="昵称"
          name="nickname"
          rules={[{ required: true, message: '请输入昵称' }]}
          labelCol={{ span: 4 }}
          style={{ marginBottom: 24 }}
        >
          <Input placeholder="请输入昵称" autoComplete="off" />
        </Form.Item>
        <Form.Item label="邮件" name="email" labelCol={{ span: 4 }} style={{ marginBottom: 24 }}>
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <Form.Item label="手机" name="phone" labelCol={{ span: 4 }}>
          <Input placeholder="请输入手机号" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AccountSettingModal;
