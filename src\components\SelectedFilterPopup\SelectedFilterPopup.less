.selected-filter-popup {
  &-content {
    min-width: 320px;
    max-width: 400px;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-title {
      font-weight: 600;
      font-size: 16px;
    }

    &-clear {
      font-size: 20px;
      cursor: pointer;
    }
  }

  &-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    &-empty {
      color: #bfbfbf;
    }
  }

  &-tag {
    margin-bottom: 8px;
    font-size: 16px;
    padding: 4px 12px;
    border-radius: 8px;
    background: #f5f7fa;
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid #e6eaf2 !important;
  }
}

:global {
  .ant-popover {
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  }
}
