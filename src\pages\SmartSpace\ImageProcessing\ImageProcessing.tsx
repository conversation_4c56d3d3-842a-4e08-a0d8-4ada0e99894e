'use client';

import type React from 'react';
import { useState, useEffect, useRef } from 'react';
import { Input, Select, Card, Tag, Button, Dropdown, Image, Space, message, Spin } from 'antd';
import type { MenuProps } from 'antd';
import {
  SearchOutlined,
  HeartOutlined,
  DownloadOutlined,
  StarFilled,
  InfoCircleOutlined,
  PlayCircleOutlined,
  DownOutlined,
} from '@ant-design/icons';
import './ImageProcessing.less';
import bg from '../../../images/fk8eh8p3.svg';
import ModalDetailModel from '../../../components/ModalDetailModel/ModalDetailModel';
import DeployConfirmModal from '../../../components/DeployConfirmModal/DeployConfirmModal';
import InferenceDeployModal from '../../../components/InferenceDeployModal/InferenceDeployModal';
import image_1749549882494_wpwop3 from '../../../images/image_1749549882494_wpwop3.svg';
import image_1749549891372_jueys0 from '../../../images/image_1749549891372_jueys0.svg';
import image_1749549898487_81scnv from '../../../images/image_1749549898487_81scnv.svg';
import { pipeline_modelview_add, getAihubDocs, getAihubApiList, getTagList } from '../../../api/kubeflowApi';
import moment from 'moment';
import Cookies from 'js-cookie';
import InfiniteScroll from 'react-infinite-scroll-component';
import debounce from 'lodash/debounce';
import { handleModelTrain } from '../../../util';

const { Option } = Select;

// 模型数据接口定义
interface ModelData {
  created_on: string; // 创建时间
  field: string; // 领域
  id: number; // id
  name: string; // 名称
  price: number; // 价格
  scenes: string; // 场景
  status: string; // 状态
  type: string; // 类型
  uuid: string; // 唯一标识
  view: number; // 浏览量
  hot: number; // 热度
  iconBg: string; // 图标背景色
  icon: string; // 图标
  tags: any[]; // 标签
  description: string; // 描述
  pipeline: string; // 流水线
}

// 推荐下拉菜单选项
const recommendItems: MenuProps['items'] = [
  { key: '最新', label: '最新' },
  { key: '最热', label: '最热' },
  { key: '推荐', label: '推荐' },
];

const ImageProcessing: React.FC = () => {
  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  // 标签数据
  const [tags, setTags] = useState<any[]>([]);
  // 卡片数据
  const [cardData, setCardData] = useState<ModelData[]>([]);
  const [selectedModel, setSelectedModel] = useState<ModelData | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hoveredCardId, setHoveredCardId] = useState<string | null>(null);
  const [showHeader, setShowHeader] = useState(false);
  const userName = Cookies.get('myapp_username');
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  // 部署相关弹窗状态
  const [deployConfirmVisible, setDeployConfirmVisible] = useState(false);
  const [inferenceDeployVisible, setInferenceDeployVisible] = useState(false);
  const [selectedDeployModel, setSelectedDeployModel] = useState<ModelData | null>(null);

  const debouncedFetchAIModelList = useRef(
    debounce((search: string, field: string) => {
      fetchAIModelList(search, field, 1, false);
    }, 500),
  );

  // 添加滚动监听
  useEffect(() => {
    const container = document.querySelector('.marketplace-main');
    const handleScroll = () => {
      if (container) {
        const scrollTop = container.scrollTop;
        setShowHeader(scrollTop > 400); // 当滚动超过400px时显示header
      }
    };

    container?.addEventListener('scroll', handleScroll);
    return () => container?.removeEventListener('scroll', handleScroll);
  }, []);

  // 搜索和筛选逻辑
  useEffect(() => {
    getAihubDocs();
  }, []);
  // 获取tag列表
  useEffect(() => {
    getTagList().then((res) => {
      const { status, data } = res;
      if (status === 200) {
        // level 与 title 的映射
        const levelTitleMap: Record<number, string> = {
          1: '类别',
          2: '模式',
          3: '任务',
          4: '来源',
          // 你有更多 level 可以继续加
        };
        // 按 level 分类
        const categories = Object.entries(levelTitleMap)
          .map(([level, title]) => {
            const options = data.result.data
              .filter((item: any) => item.level === Number(level))
              .map((item: any) => ({
                key: item.key || item.name, // 取 key 字段，没有就用 name
                label: item.name,
                id: item.id,
              }));
            return {
              title,
              options,
            };
          })
          .filter((category) => category.options.length > 0); // 只保留有内容的分类

        setTags(categories);
      }
    });
  }, []);
  // 获取AI模型列表的函数
  const fetchAIModelList = (search: string, field: string, pageNum = 1, append = false) => {
    const params = { search, field, page: pageNum, page_size: pageSize };
    setLoading(true);
    getAihubApiList(params).then((res) => {
      const { status, data } = res;
      if (status === 200) {
        const newData = data.result.data.map((item: ModelData) => ({
          ...item,
          created_on: moment(item.created_on).format('YYYY-MM-DD HH:mm:ss'),
          iconBg:
            '#' +
            Math.floor(Math.random() * 16777215)
              .toString(16)
              .padStart(6, '0'), // 随机背景色
          likes: Math.floor(Math.random() * 1000),
        }));
        setCardData((prev) => (append ? [...prev, ...newData] : newData));
        setHasMore(newData.length === pageSize);
        setTotal(data.result.total);
      }
      setLoading(false);
    });
  };
  // 监听搜索和分类变更，重置分页
  useEffect(() => {
    setPage(1);
    setHasMore(true);
    setCardData([]);
    debouncedFetchAIModelList.current(searchValue, selectedCategory);
  }, [searchValue, selectedCategory]);

  // 处理查看详情
  const handleViewDetail = (model: ModelData) => {
    setSelectedModel(model);
    setDetailVisible(true);
    setLoading(true);
    // 这里可以添加获取详细数据的逻辑
    setTimeout(() => {
      setLoading(false);
    }, 500);
  };

  // 前往训练
  const handleTrain = (model: ModelData) => {
    handleModelTrain(model);
  };

  // 前往部署
  const handleDeploy = (model: ModelData) => {
    setSelectedDeployModel(model);
    setDeployConfirmVisible(true);
  };

  // 确认部署，显示部署配置弹窗
  const handleConfirmDeploy = () => {
    setDeployConfirmVisible(false);
    setInferenceDeployVisible(true);
  };

  // 部署成功回调
  const handleDeploySuccess = (result: any) => {
    console.log('部署成功:', result);
    // 这里可以添加部署成功后的逻辑，比如刷新列表、跳转页面等
  };

  // 加载更多
  const loadMoreData = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchAIModelList(searchValue, selectedCategory, nextPage, true);
  };

  // 渲染模型卡片
  const renderModelCard = (model: ModelData) => (
    <Card
      key={model.uuid}
      className="model-card"
      hoverable
      onMouseEnter={() => setHoveredCardId(model.uuid)}
      onMouseMove={() => setHoveredCardId(model.uuid)}
      onMouseLeave={() => setHoveredCardId(null)}
    >
      <div className="model-header">
        <h3 className="model-name">{model.name}</h3>
        <Tag className={`category-tag tcm-tag`}>{model.field}</Tag>
      </div>

      <Image className="model-image" src={bg} preview={false} />

      <div className="model-stats">
        {hoveredCardId === model.uuid ? (
          <>
            <Button type="primary" onClick={() => handleViewDetail(model)}>
              详情
            </Button>
            {model.type.includes('train') && (
              <Button type="primary" onClick={() => handleTrain(model)}>
                训练
              </Button>
            )}
            {model.type.includes('inference') && (
              <Button type="primary" className="inference-btn" onClick={() => handleDeploy(model)}>
                部署
              </Button>
            )}
          </>
        ) : (
          <>
            <div className="stats-left">
              <span className="stat-item">
                <img src={image_1749549891372_jueys0} alt="" /> {model.view || 0}
              </span>
              <span className="stat-item">
                <img src={image_1749549898487_81scnv} alt="" /> {model.hot || 0}
              </span>
            </div>
            <div className="stats-right">更新于 {model.created_on}</div>
          </>
        )}
      </div>
    </Card>
  );

  return (
    <div className="image-processing-container">
      {/* 原始头部区域 */}
      <header className={`marketplace-header-bg ${showHeader ? 'hide' : ''}`}>
        <div className="marketplace-header-content">
          <h1 className="marketplace-title">图像后处理</h1>
          <div className="marketplace-desc">实现多维重建与智能优化，提升图像质量与处理效率，赋能精准诊断与创意表达​</div>
          <div className="marketplace-search-bar">
            <Input
              className="marketplace-search-input"
              placeholder="搜索关键词，如：分割、膜腺、脑部"
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onPressEnter={() => {
                fetchAIModelList(searchValue, selectedCategory);
              }}
              suffix={
                <Button
                  type="primary"
                  size="large"
                  onClick={() => {
                    fetchAIModelList(searchValue, selectedCategory);
                  }}
                >
                  搜索
                </Button>
              }
            />
          </div>
        </div>
      </header>

      {/* 固定头部区域 */}
      <header className={`marketplace-header ${showHeader ? 'show' : ''}`}>
        <div className="header-left">
          <h1 className="site-title">图像后处理</h1>
        </div>
        <div className="header-right">
          <Input
            className="search-input"
            placeholder="请输入关键词"
            prefix={<SearchOutlined />}
            suffix={<span className="search-shortcut">⌘ K</span>}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onPressEnter={() => {
              fetchAIModelList(searchValue, selectedCategory);
            }}
          />
        </div>
      </header>

      {/* 工具栏 独占一行 */}
      <div className={`marketplace-toolbar ${showHeader ? 'hide' : ''}`}>
        <div className="favorites-section">
          <img src={image_1749549882494_wpwop3} alt="" />
          <span>我的收藏</span>
        </div>
        <div className="recommend-select-wrapper">
          <span className="favorites-count">
            找到 <span className="favorites-count-number">{total}</span> 个模型
          </span>
          <Dropdown menu={{ items: recommendItems }} trigger={['click']} placement="bottomRight">
            <Button className="filter-button" type="text">
              推荐 <DownOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* 主体内容区域 */}
      <div className="marketplace-content">
        {/* 侧边栏 */}
        <aside className={`marketplace-sidebar ${showHeader ? 'auto-height' : ''}`}>
          {tags.map((cat) => (
            <div className="category-section" key={cat.title}>
              <h3 className="section-title">{cat.title}</h3>
              <div className="category-list">
                {cat.options.map((item: any) => (
                  <div
                    key={item.key}
                    className={`category-item ${selectedCategory === item.label ? 'active' : ''}`}
                    onClick={() => setSelectedCategory(selectedCategory === item.label ? '' : item.label)}
                  >
                    {item.label}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </aside>

        {/* 主要内容区域 */}
        <main className={`marketplace-main ${showHeader ? 'show-header' : ''}`} id="marketplace-main">
          <InfiniteScroll
            dataLength={cardData.length}
            next={loadMoreData}
            hasMore={hasMore}
            loader={
              <div style={{ textAlign: 'center', padding: 16 }}>
                <Spin spinning={loading} />
              </div>
            }
            scrollableTarget="marketplace-main"
          >
            <div className="models-grid">{cardData.map(renderModelCard)}</div>
          </InfiniteScroll>
        </main>
      </div>

      {/* 添加详情弹窗 */}
      <ModalDetailModel model={selectedModel} visible={detailVisible} onCancel={() => setDetailVisible(false)} />

      {/* 部署确认弹窗 */}
      <DeployConfirmModal
        visible={deployConfirmVisible}
        onCancel={() => {
          setDeployConfirmVisible(false);
          setSelectedDeployModel(null);
        }}
        onConfirm={handleConfirmDeploy}
        modelName={selectedDeployModel?.name}
      />

      {/* 推理部署配置弹窗 */}
      <InferenceDeployModal
        visible={inferenceDeployVisible}
        onCancel={() => {
          setInferenceDeployVisible(false);
          setSelectedDeployModel(null);
        }}
        onSuccess={handleDeploySuccess}
        modelData={selectedDeployModel}
      />
    </div>
  );
};

export default ImageProcessing;
