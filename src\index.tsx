import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import reportWebVitals from './reportWebVitals';
import './index.less';
import zhCN from 'antd/lib/locale/zh_CN';
import en from 'antd/lib/locale/en_US';
import { ConfigProvider, Spin } from 'antd';
import { getI18n } from 'react-i18next';
import './store/index';
import './locales/i18n';

import { BrowserRouter, useLocation, useRoutes, Navigate } from 'react-router-dom';
import { setTheme } from './theme';
import LoadingStar from './components/LoadingStar/LoadingStar';
import globalConfig from './global.config';
import Login from './pages/Login';
import Privacy from './pages/Privacy';
import Terms from './pages/Terms';

Spin.setDefaultIndicator(<LoadingStar />);

setTheme(globalConfig.theme);

const AppRoutes = () => {
  const location = useLocation();

  // 如果是登录页面，直接显示登录组件
  if (location.pathname === '/login') {
    return <Login />;
  }
  if (location.pathname === '/privacy') {
    return <Privacy />;
  }
  if (location.pathname === '/terms') {
    return <Terms />;
  }

  // 已登录，显示主应用
  return <App />;
};

const RedirectWrapper = ({ children }: { children: React.ReactNode }) => {
  const location = window.location;

  React.useEffect(() => {
    if (location.pathname === '/frontend') {
      window.location.replace(`${location.pathname}/`);
    }
  }, []);

  return <>{children}</>;
};

ReactDOM.render(
  <ConfigProvider locale={getI18n().language === 'zh-CN' ? zhCN : en}>
    <RedirectWrapper>
      <BrowserRouter basename={process.env.REACT_APP_BASE_ROUTER || '/'}>
        <AppRoutes />
      </BrowserRouter>
    </RedirectWrapper>
  </ConfigProvider>,
  document.getElementById('root'),
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
