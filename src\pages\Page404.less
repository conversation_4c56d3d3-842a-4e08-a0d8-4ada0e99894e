.page-404 {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  &__container {
    width: 100%;
    max-width: 600px;
    text-align: center;
  }

  &__content {
    background: #fff;
    border-radius: 8px;
    padding: 60px 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &__icon {
    margin-bottom: 30px;
  }

  &__number {
    font-size: 80px;
    font-weight: 700;
    color: var(--ant-primary-color);
    line-height: 1;
    margin: 0;

    @media (max-width: 768px) {
      font-size: 80px;
    }
  }

  &__text {
    color: #333;
  }

  &__title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: #333;

    @media (max-width: 768px) {
      font-size: 24px;
    }
  }

  &__description {
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 40px 0;
    color: #666;

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  &__actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;

    @media (max-width: 480px) {
      flex-direction: column;
      align-items: center;
    }
  }
}
