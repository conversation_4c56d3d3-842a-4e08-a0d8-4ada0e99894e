import React from 'react';
import { Space, Typography } from 'antd';
import { SafetyOutlined, FileTextOutlined } from '@ant-design/icons';
import './index.less';

const { Link } = Typography;

interface LegalLinksProps {
  className?: string;
  style?: React.CSSProperties;
  theme?: 'light' | 'dark';
  size?: 'small' | 'default' | 'large';
}

const LegalLinks: React.FC<LegalLinksProps> = ({ className = '', style = {}, theme = 'light', size = 'default' }) => {
  const handlePrivacyClick = () => {
    window.open('/frontend/privacy', '_blank');
  };

  const handleTermsClick = () => {
    window.open('/frontend/terms', '_blank');
  };

  return (
    <div className={`legal-links ${theme} ${size} ${className}`} style={style}>
      <Space size="large" wrap>
        <Link onClick={handlePrivacyClick} className="legal-link privacy-link">
          <SafetyOutlined className="legal-icon" />
          隐私政策
        </Link>
        <Link onClick={handleTermsClick} className="legal-link terms-link">
          <FileTextOutlined className="legal-icon" />
          用户协议
        </Link>
      </Space>
    </div>
  );
};

export default LegalLinks;
