import md5 from 'md5';

interface BaiduTranslateError {
  error_code: string;
  error_msg: string;
}

interface BaiduTranslateResponse {
  trans_result?: Array<{
    src: string;
    dst: string;
  }>;
  error_code?: string;
  error_msg?: string;
}

// 百度翻译函数
const translateBaidu = async (text: string) => {
  const appid = ''; // 请替换为您的appid
  const appkey = ''; // 请替换为您的密钥
  const salt = Math.floor(Math.random() * 100000).toString();
  const from = 'en';
  const to = 'zh';
  const sign = md5(appid + text + salt + appkey);

  const url = process.env.NODE_ENV === 'production' ? 'https://fanyi-api.baidu.com/api/trans/vip/translate' : '/translate';

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        q: text,
        from,
        to,
        appid,
        salt,
        sign,
        needIntervene: '0',
      }),
    });

    const data = (await response.json()) as BaiduTranslateResponse;

    // 检查百度翻译API的错误码
    if (data?.error_code) {
      console.error(`翻译错误 [${data.error_code}]: ${data.error_msg || '未知错误'}`);
      throw new Error(`翻译服务错误: ${data.error_msg || '未知错误'}`);
    }

    if (data?.trans_result) {
      return data.trans_result;
    }
    return text;
  } catch (error: unknown) {
    console.error('翻译出错:', error);
    // 如果是服务关闭错误，返回更友好的提示
    if (error instanceof Error && error.message.includes('58002')) {
      throw new Error('翻译服务暂时不可用，请稍后再试');
    }
    throw new Error('翻译失败，请稍后重试');
  }
};

export default translateBaidu;
