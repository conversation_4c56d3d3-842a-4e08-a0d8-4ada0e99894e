import React, { useState, useEffect, useRef } from 'react';
import { Menu, Dropdown, Popover, Modal, Button, Image, Space } from 'antd';
import {
  RightOutlined,
  ArrowRightOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  EllipsisOutlined,
  SettingOutlined,
  MailOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import './Sidebar.less';
import AccountSettingModal from './components/AccountSettingModal/AccountSettingModal';
import UserFeedbackModal from './components/UserFeedbackModal/UserFeedbackModal';
import iww1fdoo from '../../images/iww1fdoo.svg';
import image_1749778676426_67xcu2 from '../../images/image_1749778676426_67xcu2.svg';
import image_1749778682095_y1ox37 from '../../images/image_1749778682095_y1ox37.svg';
import image_1749778688034_y6wjvs from '../../images/image_1749778688034_y6wjvs.svg';
import image_1749778669664_kkgs34 from '../../images/image_1749778669664_kkgs34.svg';
import image_1749723592408_tkbxng from '../../images/image_1749723592408_tkbxng.svg';
import Cookies from 'js-cookie';

// 定义菜单项类型
interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  rightIcon?: React.ReactNode;
}

const SidebarNav: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const [accountModalOpen, setAccountModalOpen] = useState(false);
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);
  const [logoutModalOpen, setLogoutModalOpen] = useState(false);
  const [userMenuVisible, setUserMenuVisible] = useState(false);
  const userName = Cookies.get('myapp_username');

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const pathname = location.pathname;
    // 从路径中提取菜单key
    const paths = pathname.split('/').filter(Boolean);
    if (paths.length >= 2) {
      // 根据路径结构返回最后一个部分作为key
      return paths[paths.length - 1];
    }
    return '';
  };

  // 获取当前展开的子菜单
  const getOpenKeys = () => {
    const pathname = location.pathname;
    const paths = pathname.split('/').filter(Boolean);
    if (paths.length >= 1) {
      // 返回第一级路径作为展开的子菜单key
      return [paths[0].replace('-', '-')];
    }
    return [];
  };

  // 处理菜单点击事件
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
      // 智研空间
      case 'ai-model':
        navigate('/smart-space/ai-model');
        break;
      case 'image-processing':
        navigate('/smart-space/image-processing');
        break;
      case 'quantitative-analysis':
        navigate('/smart-space/quantitative-analysis');
        break;
      case 'medical-statistics':
        navigate('/smart-space/medical-statistics');
        break;

      // 开源数据库
      case 'tcia':
        navigate('/open-database/tcia');
        break;
      case 'tcga':
        navigate('/open-database/tcga');
        break;
      case 'uk-biobank':
        navigate('/open-database/uk-biobank');
        break;

      // 个人空间
      case 'project-management':
        navigate('/personal-space/project-management');
        break;
      case 'data-management':
        navigate('/personal-space/data-management');
        break;
      case 'inference-service':
        navigate('/personal-space/inference-service');
        break;
    }
  };

  // 处理收缩/展开
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 处理退出登录
  const handleLogout = () => {
    setLogoutModalOpen(true);
  };

  const handleLogoutConfirm = () => {
    setLogoutModalOpen(false);
    navigate('/login');
  };

  // 菜单数据
  const menuItems = [
    {
      key: 'smart-space',
      label: '智研空间',
      children: [
        {
          key: 'ai-model',
          label: 'AI模型商城',
        },
        {
          key: 'image-processing',
          label: '图像后处理',
        },
        // {
        //   key: 'quantitative-analysis',
        //   label: '定量分析',
        // },
        // {
        //   key: 'medical-statistics',
        //   label: '医学统计分析',
        // },
      ],
      icon: <div className="menu-icon-1" />,
    },
    {
      key: 'open-database',
      label: '开源数据库',
      icon: <div className="menu-icon-2" />,
      children: [
        {
          key: 'tcia',
          label: 'TCIA',
        },
        {
          key: 'tcga',
          label: 'TCGA',
        },
        {
          key: 'uk-biobank',
          label: 'UK Biobank',
        },
      ],
    },
    {
      key: 'personal-space',
      label: '个人空间',
      icon: <div className="menu-icon-3" />,
      children: [
        {
          key: 'project-management',
          label: '项目管理',
        },
        {
          key: 'data-management',
          label: '数据管理',
        },
        {
          key: 'inference-service',
          label: '推理服务',
        },
      ],
    },
  ];

  return (
    <div className={`sidebar-container ${collapsed ? 'collapsed' : ''}`}>
      {/* Logo区域 */}
      <div className="sidebar-header">
        {!collapsed && <div className="logo-icon" onClick={() => navigate('/')} />}
        <img src={iww1fdoo} alt="" onClick={() => toggleCollapsed()} />
      </div>
      <div className="sidebar-line" />
      {/* 菜单区域 */}
      <div className="sidebar-menu">
        <Menu
          mode="inline"
          className="custom-menu"
          items={menuItems}
          selectedKeys={[getSelectedKey()]}
          defaultOpenKeys={['smart-space', 'open-database', 'personal-space']}
          expandIcon={({ isOpen }) => <RightOutlined rotate={isOpen ? 90 : 0} />}
          onClick={handleMenuClick}
          inlineCollapsed={collapsed}
        />
      </div>
      {/* 底部用户信息 */}
      <div className="sidebar-footer" style={{ borderRadius: !userMenuVisible ? '12px' : '0px 0px 12px 12px' }}>
        <div
          className="user-info"
          onClick={(e) => {
            if (!collapsed) {
              setUserMenuVisible(!userMenuVisible);
            } else {
              toggleCollapsed();
            }
          }}
        >
          <Image src={image_1749723592408_tkbxng} style={{ minWidth: '32px', borderRadius: '50%' }} preview={false} />
          {!collapsed && <span className="username">{userName}</span>}
          <Image
            src={image_1749778669664_kkgs34}
            style={{
              transform: userMenuVisible ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease-in-out',
              cursor: 'pointer',
            }}
            preview={false}
          />
        </div>
        {/* 个人中心*/}
        {userMenuVisible && (
          <div className="user-menu">
            <div className="user-menu-item" onClick={() => setAccountModalOpen(true)}>
              <img src={image_1749778688034_y6wjvs} style={{ height: 20 }} alt="" />
              <span>账号设置</span>
            </div>
            <div className="user-menu-item" onClick={() => setFeedbackModalOpen(true)}>
              <img src={image_1749778682095_y1ox37} style={{ height: 20 }} alt="" />
              <span>用户反馈</span>
            </div>
            <div className="user-menu-item" onClick={handleLogout}>
              <img src={image_1749778676426_67xcu2} style={{ height: 20 }} alt="" />
              <span>退出登录</span>
            </div>
          </div>
        )}
      </div>
      {/* 账号设置弹窗 */}
      <AccountSettingModal
        open={accountModalOpen}
        onCancel={() => setAccountModalOpen(false)}
        onOk={() => setAccountModalOpen(false)}
        initialValues={{ nickname: userName, email: '<EMAIL>', phone: '186****8888' }}
      />
      {/* 用户反馈弹窗 */}
      <UserFeedbackModal
        open={feedbackModalOpen}
        onCancel={() => setFeedbackModalOpen(false)}
        onSubmit={() => setFeedbackModalOpen(false)}
      />
      {/* 退出登录确认框 */}
      <Modal
        className="common-modal"
        width={400}
        title="确认退出登录"
        open={logoutModalOpen}
        onOk={handleLogoutConfirm}
        onCancel={() => setLogoutModalOpen(false)}
        footer={[
          <Button key="cancel" onClick={() => setLogoutModalOpen(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" danger onClick={handleLogoutConfirm}>
            退出
          </Button>,
        ]}
      >
        <p>退出登录不会丢失任何数据，您仍可以随时登录本账号。</p>
      </Modal>
    </div>
  );
};

export default SidebarNav;
