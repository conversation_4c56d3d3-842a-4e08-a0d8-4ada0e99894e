// 主容器样式
.home-page {
  width: 100%;
  height: 100%;
  min-height: 100%;
  position: relative;
  overflow-x: hidden;

  // 视频背景样式
  .home-page__background-video {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    pointer-events: none;
  }

  .home-page__container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    min-height: 100%;
    margin: 0 auto;
    padding: 40px 0;
    z-index: 1;
    .home-page-logo {
      position: absolute;
      left: 0;
      object-fit: cover;
    }

    .home-center-title {
      margin-top: 120px;
      text-align: center;
      .home-center-title__main {
        font-size: 48px;
        font-weight: bold;
        .home-center-title__highlight {
          color: #1890ff;
        }
      }
      .home-center-title__subtitle {
        margin-top: 16px;
        font-size: 20px;
        color: #888;
      }
      .home-experience-btn-wrapper {
        position: relative;
        width: 360px;
        margin: 32px auto 0;
        cursor: pointer;

        .home-experience-btn-text {
          width: 100%;
          object-fit: cover;
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }

    .home-page__content {
      width: 100%;
      position: absolute;
      left: 50%;
      top: 72%;
      transform: translate(-50%, -50%);

      .home-page__cards {
        width: 100%;

        .home-page__card-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 32px;
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
          grid-auto-rows: min-content;
          align-items: start;
        }

        .home-page__card {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.81) 0%,
            rgba(255, 255, 255, 0.72) 61.06%,
            rgba(255, 255, 255, 0.81) 100%
          );
          border-radius: 16px;
          padding: 48px;
          width: 562px;
          height: auto;
          min-height: 200px;
          position: relative;
          overflow: hidden;
          align-self: end;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

          .home-page__card-header {
            margin-bottom: 24px;
            position: relative;
            z-index: 1;

            .home-page__card-title-wrapper {
              display: flex;
              align-items: center;
              margin-bottom: 16px;

              .home-page__card-title {
                font-size: 28px !important;
                font-weight: bold !important;
                color: rgba(0, 0, 0, 0.85) !important;
                margin: 0 !important;
              }

              .home-page__card-arrow {
                margin-left: 12px;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
              }
              .home-page__card-icon {
                width: 62px;
                height: 62px;
                object-fit: contain;
              }
            }

            .home-page__card-description {
              font-size: 16px !important;
              color: #666666 !important;
              margin-bottom: 0 !important;
              line-height: 1.6 !important;
            }
          }

          .home-page__card-icon-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 140px;
            position: relative;
            z-index: 1;

            .home-page__card-icon {
              width: 136px;
              height: 136px;
              object-fit: contain;
            }
          }

          // 卡片项目容器样式
          .home-page__card-items {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            width: 100%;
            margin-top: 24px;
          }

          // 卡片单个项目样式
          .home-page__card-item {
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            background: rgba(0, 72, 199, 0.04);
            font-size: 16px;
            color: #333;
            font-weight: 600;
            cursor: pointer;

            &:hover {
              background-color: rgba(0, 72, 199, 0.08);
            }
          }
        }
      }
    }
  }
}
