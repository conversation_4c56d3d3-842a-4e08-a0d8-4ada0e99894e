import React, { useState, useEffect } from 'react';
import { Input, Button, Card, Tag, Dropdown, Menu, Checkbox, Badge } from 'antd';
import { SearchOutlined, EyeOutlined, StarOutlined, DownOutlined, ReloadOutlined } from '@ant-design/icons';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import Sidebar from './components/Sidebar/Sidebar';
import { useRoutes, useLocation, RouteObject, useNavigate } from 'react-router-dom';
import { formatRoute, getDefaultOpenKeys, routerConfigPlus } from './routerConfig';
import { isMobileDevice } from './util';
import './App.less';

// 定义模型卡片数据接口
interface ModelCard {
  id: string;
  title: string;
  type: 'CT' | 'MR' | 'PET' | '病理';
  typeColor: string;
  image: string;
  views: number;
  stars: number;
  date: string;
}

// 定义任务类型选项接口
interface TaskOption {
  label: string;
  value: string;
  checked: boolean;
}

const ImageProcessing: React.FC = () => {
  const [CurrentRouteComponent, setCurrentRouteComponent] = useState<any>();
  const [sourceAppList, setSourceAppList] = useState<any[]>([]);
  const [sourceAppMap, setSourceAppMap] = useState<Record<string, any>>({});
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const location = useLocation();
  const navigate = useNavigate();

  // 移动端重定向逻辑
  useEffect(() => {
    // 检查是否为移动设备且不在移动端页面
    if (isMobileDevice() && location.pathname !== '/mobile-access') {
      // 直接重定向到移动端提示页面
      navigate('/mobile-access');
      return;
    } else if (!isMobileDevice() && location.pathname === '/mobile-access') {
      navigate('/');
      return;
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    // 直接使用 routerConfigPlus 作为路由配置
    const tarRoute = [...routerConfigPlus];
    const tarRouteMap = getRouterMap(tarRoute);

    setSourceAppList(tarRoute);
    setSourceAppMap(tarRouteMap);

    const defaultOpenKeys = getDefaultOpenKeys(tarRoute);
    setOpenKeys(defaultOpenKeys);

    setCurrentRouteComponent(() => () => RouterConfig(tarRoute as RouteObject[]));
  }, []);

  const getRouterMap = (routerList: any[]): Record<string, any> => {
    const res: Record<string, any> = {};
    const queue = [...routerList];
    while (queue.length) {
      const item = queue.shift();
      if (item) {
        res[item?.path || ''] = item;
        if (item?.children && item.children.length) {
          queue.push(...item.children);
        }
      }
    }
    return res;
  };

  const RouterConfig = (config: RouteObject[]) => {
    let element = useRoutes(config);
    return element;
  };

  // 格式化数字显示（如：12.8k）
  const formatNumber = (num: number): string => {
    return num >= 1000 ? `${(num / 1000).toFixed(1)}k` : num.toString();
  };

  // 不显示侧边栏的页面路径
  const noSidebarPaths = ['/', '/showOutLink', '/mobile-access'];
  const shouldShowSidebar = !noSidebarPaths.includes(location.pathname);

  return (
    <div className="image-processing">
      {/* 左侧导航栏 */}
      {shouldShowSidebar && <Sidebar />}

      {/* 主内容区域 */}
      <div className="image-processing__main">
        {/* 路由组件渲染 */}
        {CurrentRouteComponent && <CurrentRouteComponent />}
      </div>
    </div>
  );
};

export default ImageProcessing;
