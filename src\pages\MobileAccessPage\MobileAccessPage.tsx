import React, { useState, useEffect } from 'react';
import { Card, Typography } from 'antd';
import { ExclamationCircleOutlined, MobileOutlined, DesktopOutlined } from '@ant-design/icons';
import { isMobileDevice, getDeviceType } from '../../util';
import './MobileAccessPage.less';

const { Title, Text, Paragraph } = Typography;

const MobileAccessPage: React.FC = () => {
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    deviceType: 'desktop' as 'mobile' | 'tablet' | 'desktop',
    userAgent: '',
    screenWidth: 0,
    screenHeight: 0,
    isTouchDevice: false,
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      setDeviceInfo({
        isMobile: isMobileDevice(),
        deviceType: getDeviceType(),
        userAgent: navigator.userAgent,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      });
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);

    // 如果是移动设备，添加防滚动样式
    if (isMobileDevice()) {
      document.documentElement.classList.add('mobile-no-scroll');
      document.body.classList.add('mobile-no-scroll');
    }

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      // 清理防滚动样式
      document.documentElement.classList.remove('mobile-no-scroll');
      document.body.classList.remove('mobile-no-scroll');
    };
  }, []);

  // 如果是移动设备，显示移动端访问提示页面
  if (deviceInfo.isMobile) {
    return (
      <div className="mobile-access-page">
        <Card className="mobile-card">
          <div className="mobile-content">
            <div className="icon-container">
              <ExclamationCircleOutlined className="warning-icon" />
              <Title level={3} className="title">
                移动端访问提示
              </Title>
            </div>

            <Paragraph className="description">您正在使用移动设备访问，建议使用电脑端浏览器以获得最佳体验</Paragraph>

            <div className="device-info">
              <div className="info-content">
                <div className="info-item">
                  <MobileOutlined className="info-icon" />
                  <span>
                    设备类型: <strong>{deviceInfo.deviceType}</strong>
                  </span>
                </div>
                <div className="info-item">
                  <DesktopOutlined className="info-icon" />
                  <span>
                    屏幕尺寸:{' '}
                    <strong>
                      {deviceInfo.screenWidth} × {deviceInfo.screenHeight}
                    </strong>
                  </span>
                </div>
              </div>
            </div>

            <div className="recommendation">
              <Text className="main-text">💻 推荐使用电脑端访问</Text>
              <Text className="sub-text">更好的界面体验 · 完整的功能支持</Text>
            </div>
          </div>
        </Card>

        <div className="mobile-footer">建议使用Chrome、Safari等现代浏览器访问</div>
      </div>
    );
  }

  // 如果是桌面设备，显示正常的设备信息页面
  // 已移除桌面设备信息页面逻辑
  return null;
};

export default MobileAccessPage;
