// 整体容器样式
.dataset-management-container {
  height: 100%;
  padding: 40px 60px;
  background-color: #fff;
  overflow: hidden;

  .header-container {
    position: relative;
    margin-bottom: 28px;
    border-bottom: 1px solid #e6eaf2;
    h5 {
      height: 48px;
      line-height: 48px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 0;
    }

    .view-type-segmented {
      background-color: #f5f7fa;
      position: absolute;
      right: 0;
      bottom: -1px;
      .ant-segmented-item-label {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 48px;
        min-height: 48px;
        line-height: 1;
      }
    }
  }

  .search-area {
    margin-bottom: 28px;

    .search-item {
      width: 100%;

      .ant-input,
      .ant-select,
      .ant-picker {
        width: 100%;
      }
    }

    .button-group {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 24px;
      .ant-btn.ant-btn-primary {
        width: 96px;
      }
    }
  }

  .table-area {
    .task-table {
      .status-dot {
        &.success {
          color: #52c41a;
        }
        &.failed {
          color: #ff4d4f;
        }
      }
    }
    .pagination-area {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 40px;
      padding-top: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;
      .pagination-total {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .batch-operation {
      width: 100%;
      height: 80px;
      display: flex;
      align-items: center;
    }
  }

  .operation-button {
    padding: 4px 8px;
    min-width: auto;

    &:hover {
      background-color: rgba(0, 0, 0, 0.025);
    }
  }

  .card-grid {
    overflow-x: hidden;
    overflow-y: auto;
    min-height: 200px;
  }

  .dataset-card {
    background: #f5f7fa;
    border-radius: 12px;
    padding: 24px;
    height: 244px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 210px;
    transition:
      box-shadow 0.2s,
      border 0.2s;
    cursor: pointer;

    &.selected {
      border: 2px solid var(--ant-primary-color);
      box-shadow: 0 0 8px rgba(24, 144, 255, 0.15);
    }
    .ant-card-body {
      padding: 0;
    }

    .card-main {
      display: flex;
      gap: 16px;
      align-items: flex-start;
    }

    .card-img {
      width: 64px;
      height: 64px;
      border-radius: 8px;
      background: #f5f5f5;
      overflow: hidden;
      flex-shrink: 0;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }
    }

    .card-info {
      flex: 1;
      min-width: 0;
      .card-title-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        .card-title-row-left {
          height: 64px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .card-title {
            font-size: 18px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
          }
          .card-en {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
    .card-desc {
      height: 48px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      margin-top: 16px;
      line-height: 1.5;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .card-line {
      width: 100%;
      height: 1px;
      background-color: rgba(0, 0, 0, 0.04);
      margin-top: 16px;
      margin-bottom: 16px;
    }

    .card-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;
      .card-year {
        font-size: 13px;
        color: #999;
      }
      .card-actions {
        display: flex;
        gap: 8px;
        .ant-btn {
          padding: 0 20px;
          min-width: 36px;
          height: 36px;
        }
        .ant-btn:last-child {
          padding: 0 12px;
        }
      }
    }
  }

  .add-dataset-card {
    background: #fff;
    border: 1.5px dashed #b2bac7;
    border-radius: 12px;
    padding: 24px;
    height: 244px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--ant-primary-color);
    font-size: 16px;
    cursor: pointer;
    transition:
      border-color 0.2s,
      background 0.2s;
    &:hover {
      border-color: var(--ant-primary-color);
      background: #f7fbff;
    }
    .add-btn {
      margin-bottom: 8px;
      .ant-btn {
        font-size: 16px;
        height: 40px;
        padding: 0 24px;
      }
    }
    .desc {
      color: #999;
      font-size: 13px;
    }
  }
  // 覆盖 antd 默认样式
  // 基础表单控件统一样式
  .ant-input,
  .ant-picker {
    height: 48px !important;
    font-size: 14px;
    line-height: normal !important;
  }
  .ant-input-affix-wrapper {
    height: 48px !important;
    font-size: 14px;
    line-height: normal !important;

    .ant-input {
      height: 100% !important;
      padding: 0 !important;
    }
  }

  // Select 样式(不包括分页器)
  .ant-select:not(.ant-pagination-options-size-changer) {
    height: 48px !important;
    .ant-select-selector {
      height: 48px !important;
      display: flex;
      align-items: center;
    }
  }
  // 按钮
  .ant-btn.ant-btn-link {
    padding: 0;
  }

  // 表格样式
  .ant-table {
    table {
      border-radius: 2px 2px 0 0;
    }

    .ant-table-thead > tr > th {
      padding: 0 16px !important;
      background: #f5f7fa;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
      font-weight: 600;
      line-height: 60px !important;
    }

    .ant-table-tbody > tr > td {
      padding: 0 16px !important;
      line-height: 60px !important;
    }
  }
  .ant-btn {
    height: 48px;
  }
  .ant-btn.ant-gray {
    height: 48px;
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid #e6eaf2;
    background-color: #fff;
    text-shadow: none;
    box-shadow: none;
  }
}
