.dataset-modal {
  .ant-modal-body {
    display: flex;
    flex-direction: column;
    gap: 24px; // 各部分之间间距
  }

  .ant-modal-footer {
    border-top: none; // 移除 Ant Design 默认的 footer 顶部边框
    padding-top: 0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding-right: 24px;
    padding-bottom: 24px;
  }

  // Section 样式

  &__subsection-title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 8px;
    position: relative;
    padding-left: 11px; // 留出空间放置左侧竖线

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px; // 竖线宽度
      height: 16px; // 竖线高度
      background-color: var(--ant-primary-color); // 竖线颜色
      border-radius: 9px;
    }
  }

  // 表单项样式调整
  .ant-form-item {
    margin-bottom: 16px;

    .ant-form-item-label {
      > label {
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.45);
        text-align: right;
      }
    }

    // 调整水平布局下的对齐
    &.ant-form-item-horizontal {
      .ant-form-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 48px; // 与输入框高度对齐
      }
    }
  }

  &__input,
  &__textarea,
  &__select {
    width: 100%; // 确保输入框占满容器宽度
  }

  &__preview-image-wrapper {
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0; // 防止图片被压缩
  }

  // 文件上传

  &__image-placeholder {
    width: 48px;
    height: 48px;
    background-color: #f5f7fa;
    border: 1px dashed #b2bac7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0; // 防止占位符被压缩
  }
}
