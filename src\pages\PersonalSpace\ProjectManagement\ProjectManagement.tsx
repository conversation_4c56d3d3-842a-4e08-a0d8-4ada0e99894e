import React, { useState, useEffect } from 'react';
import {
  Tabs,
  Input,
  Select,
  DatePicker,
  Button,
  Table,
  Tag,
  Space,
  Tooltip,
  Row,
  Col,
  Segmented,
  Card,
  Pagination,
  Typography,
  Image,
  Popconfirm,
  Modal,
  message,
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';
import type { SegmentedProps } from 'antd/es/segmented';
import {
  SyncOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DoubleRightOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import './ProjectManagement.less';
import bg from '../../../images/fk8eh8p3.svg';
import image_1749694653385_2whmuf from '../../../images/image_1749694653385_2whmuf.svg';
import image_1749694664016_aaca5f from '../../../images/image_1749694664016_aaca5f.svg';

import image_1749696210409_y7dux2 from '../../../images/image_1749696210409_y7dux2.svg';
import { getTagList, getProjectDocs, getProjectList, updateProject, deleteProject } from '../../../api/kubeflowApi';
import moment from 'moment';

import { goToPipelinePage } from '../../../util';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Paragraph } = Typography;

// 状态映射
const STATUS_MAP = {
  Pending: '待运行',
  Running: '运行中',
  Succeeded: '成功',
  Failed: '失败',
  Terminated: '终止',
} as const;

// 状态类型
type StatusType = keyof typeof STATUS_MAP;

// 获取状态显示文本
const getStatusText = (status: string): string => {
  return STATUS_MAP[status as StatusType] || status;
};

// 判断是否为运行中状态
const isTrainingStatus = (status: string): boolean => {
  return ['training'].includes(status);
};

// 定义任务数据接口
interface TaskItem {
  key: string;
  id: number;
  pipeline_id: string;
  description: string;
  tags: any[];
  changed_on: string;
  created_on: string;
  status: string;
  report: string;
  pipeline: string;
}

// 添加自定义 hook 用于计算表格高度
const useTableHeight = () => {
  const [tableHeight, setTableHeight] = useState<number>(0);

  useEffect(() => {
    const calculateHeight = () => {
      const headerHeight = document.querySelector('.header-container')?.clientHeight || 0;
      const searchAreaHeight = document.querySelector('.search-area')?.clientHeight || 0;
      const paginationHeight = document.querySelector('.pagination-area')?.clientHeight || 0;
      const margin = 120; // 上下边距

      const height = window.innerHeight - headerHeight - searchAreaHeight - paginationHeight - margin;
      setTableHeight(Math.max(height, 200)); // 设置最小高度为 200px
    };

    calculateHeight();
    window.addEventListener('resize', calculateHeight);

    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  return tableHeight;
};

const TaskManagement: React.FC = React.memo(() => {
  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('mine');
  const [taskData, setTaskData] = useState<TaskItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [viewType, setViewType] = useState<'list' | 'card'>('list');
  const tableHeight = useTableHeight();
  const [pageInfo, setPageInfo] = useState<any>({
    page: 1,
    page_size: 10,
    total: 0,
    task_flow: '',
    description: '',
    tags: '',
    status: '',
    changed_on_end: '',
    changed_on_start: '',
    created_on_end: '',
    created_on_start: '',
  });
  // 标签数据
  const [tags, setTags] = useState<any[]>([]);

  useEffect(() => {
    getProjectDocs().then((res) => {
      // console.log(res);
    });
  }, []);

  useEffect(() => {
    getTagList().then((res) => {
      const { status, data } = res;
      if (status === 200) {
        setTags(data.result.data);
      }
    });
  }, []);

  // 获取项目管理列表
  const getProjectListData = () => {
    setLoading(true);
    const params = {
      page: pageInfo.page,
      page_size: pageInfo.page_size,
      task_flow: pageInfo.task_flow,
      description: pageInfo.description,
      tags: pageInfo.tags,
      status: pageInfo.status,
      changed_on_end: pageInfo.changed_on_end,
      changed_on_start: pageInfo.changed_on_start,
      created_on_end: pageInfo.created_on_end,
      created_on_start: pageInfo.created_on_start,
    };
    getProjectList(params).then((res) => {
      const { status, data } = res;
      if (status === 200) {
        // 如果当前页不是第一页且数据为空，自动跳转到上一页
        if (data.result.data.length === 0 && pageInfo.page > 1) {
          setPageInfo((prev: any) => ({
            ...prev,
            page: prev.page - 1,
          }));
          // 不要setLoading(false)，等useEffect触发重新请求
          return;
        }
        setPageInfo((prev: any) => ({
          ...prev,
          total: data.result.total,
        }));
        setTaskData(data.result.data);
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    getProjectListData();
  }, [
    pageInfo.page,
    pageInfo.page_size,
    pageInfo.task_flow,
    pageInfo.description,
    pageInfo.tags,
    pageInfo.status,
    pageInfo.changed_on_end,
    pageInfo.changed_on_start,
    pageInfo.created_on_end,
    pageInfo.created_on_start,
  ]);

  // 表格列定义
  const columns: ColumnsType<TaskItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务流',
      dataIndex: 'pipeline_id',
      key: 'pipeline_id',
      width: 150,
      render: (text) => <span className="task-flow-link">{text}</span>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => (
        <Tooltip title={text}>
          <Paragraph ellipsis={{ rows: 1 }} style={{ marginBottom: 0 }}>
            {text}
          </Paragraph>
        </Tooltip>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: any[]) => (
        <Space size={4}>
          {Array.isArray(tags) &&
            tags.map((tag) => {
              let color = '';
              let backgroundColor = 'rgba(0, 0, 0, 0.02)';
              if (tag.name === 'CT') {
                color = '#16B1FF';
                backgroundColor = 'rgba(22, 177, 255, 0.07)';
              } else if (tag.name === 'PET') {
                color = '#FF8D2F';
                backgroundColor = 'rgba(255, 141, 47, 0.07)';
              } else if (tag.name === 'MR') {
                color = '#56CA00';
                backgroundColor = 'rgba(86, 202, 0, 0.07)';
              } else if (tag.name === '病理') {
                color = '#BA6CFF';
                backgroundColor = 'rgba(186, 108, 255, 0.07)';
              }
              return (
                <Tag key={tag.id} className="task-tag" style={{ backgroundColor, color }}>
                  {tag.name}
                </Tag>
              );
            })}
        </Space>
      ),
    },
    {
      title: '修改时间',
      dataIndex: 'changed_on',
      key: 'changed_on',
      width: 160,
      ellipsis: true,
      render: (text) => <span style={{ whiteSpace: 'nowrap' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '创建时间',
      dataIndex: 'created_on',
      key: 'created_on',
      width: 160,
      ellipsis: true,
      render: (text) => <span style={{ whiteSpace: 'nowrap' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (status: string) => (
        <span className={`status-dot ${status}`}>
          {isTrainingStatus(status) ? (
            <Popconfirm
              title={
                <div>
                  <div style={{ fontWeight: 600, fontSize: 16, marginBottom: 4 }}>确认终止运行吗</div>
                  <div style={{ color: '#666', fontSize: 13 }}>终止后，本周期内的训练数据将无法恢复</div>
                </div>
              }
              okText="确认"
              cancelText="取消"
              onConfirm={handleStopTraining}
              placement="top"
              icon={<Image src={image_1749696210409_y7dux2} preview={false} />}
            >
              <Tooltip title="点击终止运行">
                <span className="training-status" onClick={(e) => e.stopPropagation()}>
                  {getStatusText(status)}
                </span>
              </Tooltip>
            </Popconfirm>
          ) : (
            getStatusText(status)
          )}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size={12}>
          <Button type="link" onClick={() => handlePipelineAction(record, 'edit', 'pipeline')}>
            编辑
          </Button>
          <Button type="link" onClick={() => handlePipelineAction(record, 'edit', 'log')}>
            日志
          </Button>
          <Button type="link" disabled={!record.report} onClick={() => handlePipelineAction(record, 'edit', 'report')}>
            报告
          </Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'mine',
      label: '我的',
    },
    {
      key: 'collaboration',
      label: '协作',
    },
  ];

  // 处理查询按钮点击
  const handleQuery = () => {
    getProjectListData();
  };

  // 处理重置按钮点击
  const handleReset = () => {
    setPageInfo({
      page: 1,
      page_size: 10,
      total: 0,
      task_flow: '',
      description: '',
      tags: '',
      status: '',
      changed_on_end: '',
      changed_on_start: '',
      created_on_end: '',
      created_on_start: '',
    });
  };

  // 删除项目管理记录
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确认删除该记录吗？',
      onOk: () => {
        deleteProject(id).then((res) => {
          const { data } = res;
          if (data.status === 0) {
            message.success('删除成功');
            getProjectListData();
          }
        });
      },
    });
  };

  // 处理编辑、查看日志、查看报告操作（统一跳转到 Pipeline 页面）
  const handlePipelineAction = (record: TaskItem, operation: string, enter_page: string = 'pipeline') => {
    goToPipelinePage(record.pipeline_id, record, operation, enter_page);
  };

  // 处理终止训练
  const handleStopTraining = () => {
    // TODO: 实现终止训练的逻辑
    console.log('终止训练');
  };

  // 渲染卡片视图
  const renderCardView = () => {
    return (
      <div className="card-view">
        {taskData.map((item) => (
          <Card key={item.id} className={`task-card ${item.status}`} bodyStyle={{ padding: '16px' }} bordered={false}>
            <div className="task-card-content">
              <div className="card-header">
                <div className="task-image">
                  <img src={bg} alt="任务图片" />
                </div>
                <div className="task-info">
                  <h4 className="title" title={item.description}>
                    {item.description}
                  </h4>
                  <div className="task-flow">任务流：{item.pipeline_id}</div>
                  <div className="tags-container">
                    <Space size={4}>
                      {item.tags.map((tag) => {
                        let color = '';
                        let backgroundColor = 'rgba(0, 0, 0, 0.02)';
                        if (tag.name === 'CT') {
                          color = '#16B1FF';
                          backgroundColor = 'rgba(22, 177, 255, 0.07)';
                        } else if (tag.name === 'PET') {
                          color = '#FF8D2F';
                          backgroundColor = 'rgba(255, 141, 47, 0.07)';
                        } else if (tag.name === 'MR') {
                          color = '#56CA00';
                          backgroundColor = 'rgba(86, 202, 0, 0.07)';
                        } else if (tag.name === '病理') {
                          color = '#BA6CFF';
                          backgroundColor = 'rgba(186, 108, 255, 0.07)';
                        }
                        return (
                          <Tag key={tag.id} className="task-tag" style={{ backgroundColor, color }}>
                            {tag.name}
                          </Tag>
                        );
                      })}
                    </Space>
                  </div>
                </div>
              </div>
              <div className="card-footer">
                <div className={`status-dot ${item.status}`}>
                  {isTrainingStatus(item.status) ? (
                    <Popconfirm
                      title={
                        <div>
                          <div style={{ fontWeight: 600, fontSize: 16, marginBottom: 4 }}>确认终止运行吗</div>
                          <div style={{ color: '#666', fontSize: 13 }}>终止后，本周期内的训练数据将无法恢复</div>
                        </div>
                      }
                      okText="确认"
                      cancelText="取消"
                      onConfirm={handleStopTraining}
                      placement="top"
                      icon={<span style={{ color: '#faad14', fontSize: 18, marginRight: 4 }}>!</span>}
                    >
                      <Tooltip title="点击终止训练">
                        <span className="training-dot" onClick={(e) => e.stopPropagation()}>
                          {getStatusText(item.status)}
                        </span>
                      </Tooltip>
                    </Popconfirm>
                  ) : (
                    <span className={`${item.status === 'success' ? 'success-dot' : 'failed-dot'}`}>{getStatusText(item.status)}</span>
                  )}
                </div>
                <div className="action-buttons">
                  <Button type="text" className="ant-gray" onClick={() => handlePipelineAction(item, 'edit', 'pipeline')}>
                    编辑
                  </Button>
                  <Button type="text" className="ant-gray" onClick={() => handlePipelineAction(item, 'edit', 'log')}>
                    日志
                  </Button>
                  <Button
                    type="text"
                    className="ant-gray"
                    disabled={!item.report}
                    onClick={() => handlePipelineAction(item, 'edit', 'report')}
                  >
                    报告
                  </Button>
                  <Button type="text" className="ant-gray" onClick={() => handleDelete(item.id)}>
                    删除
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  };

  // 渲染页面
  return (
    <div className="task-management-container">
      {/* 标签页和视图切换 */}
      <div className="header-container">
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} className="task-tabs" />
        {/* @ts-ignore */}
        <Segmented
          value={viewType}
          onChange={(value) => setViewType(value as 'list' | 'card')}
          options={[
            {
              value: 'list',
              icon: <Image src={image_1749694653385_2whmuf} preview={false} />,
            },
            {
              value: 'card',
              icon: <Image src={image_1749694664016_aaca5f} preview={false} />,
            },
          ]}
          className="view-type-segmented"
        />
      </div>

      {/* 搜索区域 */}
      <div className="search-area">
        <Row gutter={[24, 16]}>
          <Col span={6}>
            <div className="search-item">
              <Input
                placeholder="任务流"
                allowClear
                value={pageInfo.task_flow}
                onChange={(e) => setPageInfo({ ...pageInfo, task_flow: e.target.value })}
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="search-item">
              <Input
                placeholder="描述"
                allowClear
                value={pageInfo.description}
                onChange={(e) => setPageInfo({ ...pageInfo, description: e.target.value })}
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="search-item">
              <Select
                placeholder="标签"
                allowClear
                value={pageInfo.tags || undefined}
                onChange={(value) => setPageInfo({ ...pageInfo, tags: value })}
              >
                {tags.map((tag) => (
                  <Select.Option key={tag.id} value={tag.name}>
                    {tag.name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={6}>
            <div className="search-item">
              <Select
                placeholder="状态"
                allowClear
                value={pageInfo.status || undefined}
                onChange={(value) => setPageInfo({ ...pageInfo, status: value })}
              >
                {Object.entries(STATUS_MAP).map(([key, value]) => (
                  <Select.Option key={key} value={key}>
                    {value}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </Col>
        </Row>

        <Row gutter={[24, 16]} style={{ marginTop: '16px' }}>
          <Col span={6}>
            <div className="search-item">
              <RangePicker
                placeholder={['修改时间', '结束日期']}
                style={{ width: '100%' }}
                allowClear
                value={
                  pageInfo.changed_on_start && pageInfo.changed_on_end
                    ? [moment(pageInfo.changed_on_start), moment(pageInfo.changed_on_end)]
                    : null
                }
                onChange={(value) => {
                  if (value) {
                    setPageInfo({
                      ...pageInfo,
                      changed_on_end: value[1]?.format('YYYY-MM-DD'),
                      changed_on_start: value[0]?.format('YYYY-MM-DD'),
                    });
                  } else {
                    setPageInfo({
                      ...pageInfo,
                      changed_on_end: '',
                      changed_on_start: '',
                    });
                  }
                }}
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="search-item">
              <RangePicker
                placeholder={['创建时间', '结束日期']}
                style={{ width: '100%' }}
                allowClear
                value={
                  pageInfo.created_on_start && pageInfo.created_on_end
                    ? [moment(pageInfo.created_on_start), moment(pageInfo.created_on_end)]
                    : null
                }
                onChange={(value) => {
                  if (value) {
                    setPageInfo({
                      ...pageInfo,
                      created_on_end: value[1]?.format('YYYY-MM-DD'),
                      created_on_start: value[0]?.format('YYYY-MM-DD'),
                    });
                  } else {
                    setPageInfo({
                      ...pageInfo,
                      created_on_end: '',
                      created_on_start: '',
                    });
                  }
                }}
              />
            </div>
          </Col>
          <Col span={12} className="button-group">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleQuery}>
              查询
            </Button>
            <Button onClick={handleReset}>重置</Button>
          </Col>
        </Row>
      </div>

      {/* 表格/卡片区域 */}
      <div className="table-area">
        {viewType === 'list' ? (
          <>
            <Table
              rowKey={(record) => record.id}
              columns={columns}
              dataSource={taskData}
              loading={loading}
              className="task-table"
              pagination={false}
              scroll={{ x: 'max-content', y: tableHeight }}
              onChange={(pagination) => {
                setPageInfo({ ...pageInfo, page: pagination.current, page_size: pagination.pageSize });
              }}
            />
            {/* 分页区域 */}
            <div className="pagination-area">
              <Space className="pagination-total" size={4}>
                共计{pageInfo.total}条
              </Space>
              <Pagination
                current={pageInfo.page}
                pageSize={pageInfo.page_size}
                total={pageInfo.total}
                showSizeChanger={true}
                showQuickJumper={true}
                pageSizeOptions={['10', '20', '50', '100']}
                onChange={(page, pageSize) => {
                  setPageInfo((prev: any) => ({
                    ...prev,
                    page,
                    page_size: pageSize,
                  }));
                }}
              />
            </div>
          </>
        ) : (
          renderCardView()
        )}
      </div>
    </div>
  );
});

export default TaskManagement;
