.inference-service-detail-modal {
  .detail-content {
    max-height: 600px;
    overflow-y: auto;
    
    .ant-descriptions {
      margin-bottom: 0;
      
      &-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 16px;
      }
      
      &-item {
        &-label {
          font-weight: 500;
          color: rgba(0, 0, 0, 0.65);
          background-color: #fafafa;
        }
        
        &-content {
          color: rgba(0, 0, 0, 0.85);
          word-break: break-all;
        }
      }
    }
    
    .ant-divider {
      margin: 24px 0 16px 0;
    }
    
    // 标签样式
    .ant-tag {
      margin: 0;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
    
    // 链接内容样式
    a {
      color: #1890ff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  // 模态框样式调整
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    
    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .ant-modal-body {
    padding: 24px;
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 12px 24px;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .inference-service-detail-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }
    
    .detail-content {
      .ant-descriptions {
        &-item {
          &-label,
          &-content {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}
