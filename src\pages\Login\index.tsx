import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Checkbox } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Cookies from 'js-cookie';
import globalConfig from '../../global.config';
import './index.less';
import { userLogin } from '../../api/kubeflowApi';
import LegalLinks from '../../components/LegalLinks';

interface LoginForm {
  username: string;
  password: string;
  remember?: boolean;
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 组件加载时从localStorage获取保存的用户名和密码
  useEffect(() => {
    const savedUsername = localStorage.getItem('remembered_username');
    const savedPassword = localStorage.getItem('remembered_password');
    if (savedUsername && savedPassword) {
      form.setFieldsValue({
        username: savedUsername,
        password: savedPassword,
        remember: true,
      });
    }
  }, [form]);

  const onFinish = async (values: LoginForm) => {
    try {
      setLoading(true);

      const response = await userLogin({
        username: values.username,
        password: values.password,
      });

      const data = response.data;

      if (data.status === 0) {
        // 处理记住密码
        if (values.remember) {
          localStorage.setItem('remembered_username', values.username);
          localStorage.setItem('remembered_password', values.password);
        } else {
          localStorage.removeItem('remembered_username');
          localStorage.removeItem('remembered_password');
        }
        // 保存token
        localStorage.setItem('myapp_token', data.result.token);

        message.success(data.message || '登录成功');
        Cookies.set('myapp_username', values.username);
        navigate('/');
      } else {
        message.error(data.message || '用户名或密码错误');
      }
    } catch (error) {
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-page">
      <div className="logo-container">
        <img src={globalConfig.appLogo.default} alt="logo" />
      </div>

      <div className="login-container">
        <h2 className="login-title">登录账号</h2>

        <Form form={form} name="login" onFinish={onFinish} autoComplete="off" size="large">
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { pattern: /^[a-z][a-z0-9\-]*[a-z0-9]$/, message: '用户名只能由小写字母、数字、-组成' },
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item name="password" rules={[{ required: true, message: '请输入密码' }]}>
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <div style={{ margin: '28px 0 12px' }}>
            <Form.Item name="remember" valuePropName="checked" initialValue={true} noStyle>
              <Checkbox>记住密码</Checkbox>
            </Form.Item>
          </div>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block className="login-button">
              登录
            </Button>
          </Form.Item>
        </Form>

        <div className="legal-links-container">
          <LegalLinks theme="light" size="small" />
        </div>
      </div>
    </div>
  );
};

export default Login;
