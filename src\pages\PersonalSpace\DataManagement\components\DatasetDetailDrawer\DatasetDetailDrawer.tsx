'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import { Drawer, Button, Tag, Image } from 'antd';
import { CloseOutlined, FileOutlined } from '@ant-design/icons';
import './DatasetDetailDrawer.less';
import image_1749722936349_1k6dn2 from '../../../../../images/image_1749722936349_1k6dn2.svg';
import { getDatasetById } from '../../../../../api/kubeflowApi';

// 组件属性接口定义
interface CustomDrawerProps {
  /** 抽屉是否可见 */
  visible: boolean;
  /** 抽屉标题 */
  title?: string;
  /** 确认按钮文本 */
  okText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮回调 */
  onOk?: () => void;
  /** 取消按钮回调 */
  onCancel?: () => void;
  /** 抽屉内容数据 */
  content?: {
    id?: number;
    previewImage?: string;
    name?: string;
    chinese_name?: string;
    description?: string;
    tags?: any[];
    version?: string;
    fileName?: string;
  };
  /** 抽屉宽度 */
  width?: number | string;
}

const CustomDrawer: React.FC<CustomDrawerProps> = ({
  visible,
  title = '详情',
  okText = '上一个',
  cancelText = '下一个',
  onOk,
  onCancel,
  content = { id: 0 },
  width = '40%',
}) => {
  const [contentData, setContentData] = useState<any>({});
  useEffect(() => {
    if (visible) {
      getDatasetById(content?.id || 0).then((res) => {
        const { status, data } = res;
        if (status === 200) {
          setContentData(data.result);
        }
      });
    }
  }, [visible]);

  return (
    <Drawer
      className="common-drawer"
      title="详情"
      placement="right"
      onClose={onCancel}
      open={visible}
      width={width}
      maskClosable={true}
      destroyOnClose={true}
      // footer={[
      //   <Button key="cancel" onClick={onCancel}>
      //     上一个
      //   </Button>,
      //   <Button key="submit" onClick={onOk}>
      //     下一个
      //   </Button>,
      // ]}
    >
      <div className="custom-drawer__container">
        {/* 内容区域 */}
        <div className="custom-drawer__content">
          {/* 预览图 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">预览图</div>
            <div className="custom-drawer__value">
              <img src={contentData?.preview_image || 'https://picsum.photos/48'} alt="预览图" className="custom-drawer__preview-image" />
            </div>
          </div>

          {/* 名称 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">名称</div>
            <div className="custom-drawer__value custom-drawer__value--text">{contentData?.name}</div>
          </div>

          {/* 中文名 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">中文名</div>
            <div className="custom-drawer__value custom-drawer__value--text">{contentData?.chinese_name}</div>
          </div>

          {/* 描述 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">描述</div>
            <div className="custom-drawer__value custom-drawer__value--description">{contentData?.description}</div>
          </div>

          {/* 标签 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">标签</div>
            <div className="custom-drawer__value">
              <div className="custom-drawer__tags">
                {contentData?.tags &&
                  contentData?.tags.map((tag: any) => {
                    let color = '';
                    let backgroundColor = 'rgba(0, 0, 0, 0.02)';
                    if (tag.name === 'CT') {
                      color = '#16B1FF';
                      backgroundColor = 'rgba(22, 177, 255, 0.07)';
                    } else if (tag.name === 'PET') {
                      color = '#FF8D2F';
                      backgroundColor = 'rgba(255, 141, 47, 0.07)';
                    } else if (tag.name === 'MR') {
                      color = '#56CA00';
                      backgroundColor = 'rgba(86, 202, 0, 0.07)';
                    } else if (tag.name === '病理') {
                      color = '#BA6CFF';
                      backgroundColor = 'rgba(186, 108, 255, 0.07)';
                    }
                    return (
                      <Tag key={tag.id} className="custom-drawer__tag" style={{ backgroundColor, color }}>
                        {tag.name}
                      </Tag>
                    );
                  })}
              </div>
            </div>
          </div>

          {/* 版本 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">版本</div>
            <div className="custom-drawer__value custom-drawer__value--text">{contentData?.version}</div>
          </div>

          {/* 文件 */}
          <div className="custom-drawer__field">
            <div className="custom-drawer__label">文件</div>
            <div className="custom-drawer__value">
              <div className="custom-drawer__file">
                <Image src={image_1749722936349_1k6dn2} preview={false} className="custom-drawer__file-icon" />
                <span className="custom-drawer__file-name">{contentData?.path}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default CustomDrawer;
