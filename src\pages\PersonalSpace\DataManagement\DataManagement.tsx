import React, { useState, useEffect } from 'react';
import {
  Input,
  Select,
  Button,
  Table,
  Tag,
  Space,
  Row,
  Col,
  Typography,
  Segmented,
  Tooltip,
  Pagination,
  Checkbox,
  Card,
  Image,
  Dropdown,
  message,
  Modal,
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  PlusOutlined,
  UnorderedListOutlined,
  A<PERSON>toreOutlined,
  EyeOutlined,
  EditOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  EllipsisOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import './DataManagement.less';
import bg from '../../../images/fk8eh8p3.svg';
import AddDatasetModal from './components/AddDatasetModal/AddDatasetModal';
import EditDatasetModal from './components/EditDatasetModal/EditDatasetModal';
import DatasetDetailDrawer from './components/DatasetDetailDrawer/DatasetDetailDrawer';
import image_1749694653385_2whmuf from '../../../images/image_1749694653385_2whmuf.svg';
import image_1749694664016_aaca5f from '../../../images/image_1749694664016_aaca5f.svg';
import image_1749699967101_1lulvx from '../../../images/image_1749699967101_1lulvx.svg';
import image_1749709747172_v3psnp from '../../../images/image_1749709747172_v3psnp.svg';
import { getDatasetDocs, getDatasetList, createDataset, updateDataset, deleteDataset, uploadFile } from '../../../api/kubeflowApi';

const { Title, Paragraph } = Typography;

// 定义数据集接口
export interface DatasetItem {
  key: string;
  id: number;
  name: string;
  chinese_name: string;
  description: string;
  tags: string[];
  version: string;
  operations: string[];
}

const AddDatasetCard: React.FC<{
  onAdd: () => void;
  selectedRowKeys: string[];
}> = ({ onAdd, selectedRowKeys }) => (
  <div className="add-dataset-card" onClick={onAdd}>
    <div className="add-btn">
      <Button type="primary" icon={<PlusOutlined />} size="large" disabled={!selectedRowKeys.length}>
        添加数据集
      </Button>
    </div>
    <div className="desc">拖拽或点击，支持批量添加</div>
  </div>
);

const DatasetCard: React.FC<{
  item: DatasetItem;
  selected: boolean;
  onSelect: (key: string, checked: boolean) => void;
  showEditModal: (record: DatasetItem) => void;
  showDetailDrawer: (record: DatasetItem) => void;
}> = ({ item, selected, onSelect, showEditModal, showDetailDrawer }) => {
  const [isHovered, setIsHovered] = useState(false);
  const items = [
    {
      key: 'backup',
      label: '备份',
      icon: <CloudUploadOutlined />,
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
    },
  ];

  return (
    <Card
      className={`dataset-card${selected ? ' selected' : ''}`}
      hoverable
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="card-main">
        <div className="card-img">
          <img src={bg} alt="cover" />
        </div>
        <div className="card-info">
          <div className="card-title-row">
            <div className="card-title-row-left">
              <div className="card-title">{item.chinese_name}</div>
              <div className="card-en">{item.name}</div>
            </div>
            {(isHovered || selected) && (
              <Checkbox
                checked={selected}
                onChange={(e) => {
                  e.stopPropagation();
                  onSelect(item.key, e.target.checked);
                }}
              />
            )}
          </div>
        </div>
      </div>
      <div className="card-desc" title={item.description}>
        类型：{item.description}
      </div>
      <div className="card-line"></div>
      <div className="card-bottom">
        <div className="card-year">{item.version}</div>
        <div className="card-actions">
          <Button size="small" onClick={() => showDetailDrawer(item)}>
            详情
          </Button>
          <Button size="small" onClick={() => showEditModal(item)}>
            修改
          </Button>
          <Dropdown
            menu={{
              items,
              onClick: ({ key }) => {
                if (key === 'backup') {
                  // 处理备份操作
                  console.log('备份', item);
                } else if (key === 'delete') {
                  // 处理删除操作
                  console.log('删除', item);
                }
              },
            }}
            trigger={['click']}
          >
            <Button size="small">
              <EllipsisOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    </Card>
  );
};
// 添加自定义 hook 用于计算表格高度
const useTableHeight = (selectedRowKeys: string[]) => {
  const [tableHeight, setTableHeight] = useState<number>(0);

  useEffect(() => {
    const calculateHeight = () => {
      const headerHeight = document.querySelector('.header-container')?.clientHeight || 0;
      const searchAreaHeight = document.querySelector('.search-area')?.clientHeight || 0;
      const paginationHeight = document.querySelector('.pagination-area')?.clientHeight || 0;
      const margin = selectedRowKeys.length > 0 ? 240 : 142; // 上下边距

      const height = window.innerHeight - headerHeight - searchAreaHeight - paginationHeight - margin;
      setTableHeight(Math.max(height, 200)); // 设置最小高度为 200px
    };

    calculateHeight();
    window.addEventListener('resize', calculateHeight);

    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, [selectedRowKeys]);

  return tableHeight;
};

const DataManagement: React.FC = React.memo(() => {
  // 状态管理
  const [datasetData, setDatasetData] = useState<DatasetItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [viewType, setViewType] = useState<'list' | 'card'>('list');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [currentDataset, setCurrentDataset] = useState<DatasetItem | undefined>();
  const [detailDrawerVisible, setDetailDrawerVisible] = useState<boolean>(false);
  const tableHeight = useTableHeight(selectedRowKeys);
  const [pageInfo, setPageInfo] = useState<any>({
    page: 1,
    page_size: 10,
    total: 0,
    search: '',
    version: '',
  });

  useEffect(() => {
    getDatasetDocs().then((res) => {
      // console.log(res);
    });
  }, []);
  // 获取数据集列表
  const getDatasetListData = () => {
    const params = {
      page: pageInfo.page,
      page_size: pageInfo.page_size,
      search: pageInfo.search,
      version: pageInfo.version,
    };
    getDatasetList(params).then((res) => {
      const { status, data } = res;
      if (status === 200) {
        // 如果当前页不是第一页且数据为空，自动跳转到上一页
        if (data.result.data.length === 0 && pageInfo.page > 1) {
          setPageInfo((prev: any) => ({
            ...prev,
            page: prev.page - 1,
          }));
          // 不要setLoading(false)，等useEffect触发重新请求
          return;
        }
        setDatasetData(data.result.data);
        setPageInfo({
          ...pageInfo,
          total: data.result.total,
        });
      }
    });
  };
  // 删除数据集
  const handleDeleteDataset = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确认删除该数据集吗？',
      onOk: () => {
        deleteDataset(id).then((res) => {
          const { status } = res;
          if (status === 200) {
            getDatasetListData();
          } else {
            message.error('删除失败');
          }
        });
      },
    });
  };
  useEffect(() => {
    getDatasetListData();
  }, [pageInfo.page, pageInfo.page_size, pageInfo.search, pageInfo.version]);

  // 选择框配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  // 表格列定义
  const columns: ColumnsType<DatasetItem> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '中文名',
      dataIndex: 'chinese_name',
      key: 'chinese_name',
      width: 200,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => (
        <Tooltip title={text}>
          <Paragraph ellipsis={{ rows: 1 }} style={{ marginBottom: 0 }}>
            {text}
          </Paragraph>
        </Tooltip>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: any[]) => (
        <Space size={4}>
          {Array.isArray(tags) &&
            tags.map((tag) => {
              let color = '';
              let backgroundColor = 'rgba(0, 0, 0, 0.02)';
              if (tag.name === 'CT') {
                color = '#16B1FF';
                backgroundColor = 'rgba(22, 177, 255, 0.07)';
              } else if (tag.name === 'PET') {
                color = '#FF8D2F';
                backgroundColor = 'rgba(255, 141, 47, 0.07)';
              } else if (tag.name === 'MR') {
                color = '#56CA00';
                backgroundColor = 'rgba(86, 202, 0, 0.07)';
              } else if (tag.name === '病理') {
                color = '#BA6CFF';
                backgroundColor = 'rgba(186, 108, 255, 0.07)';
              }
              return (
                <Tag key={tag.id} className="task-tag" style={{ backgroundColor, color }}>
                  {tag.name}
                </Tag>
              );
            })}
        </Space>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
    },
    {
      title: '操作',
      key: 'operations',
      width: 200,
      render: (_, record) => (
        <Space size={12}>
          <Tooltip title="详情">
            <Button type="link" size="small" className="operation-button" onClick={() => showDetailDrawer(record)}>
              详情
            </Button>
          </Tooltip>
          <Tooltip title="修改">
            <Button type="link" size="small" className="operation-button" onClick={() => showEditModal(record)}>
              修改
            </Button>
          </Tooltip>
          <Tooltip title="备份">
            <Button type="link" size="small" className="operation-button">
              备份
            </Button>
          </Tooltip>
          <Tooltip title="删除">
            <Button type="link" size="small" danger className="operation-button" onClick={() => handleDeleteDataset(record.id)}>
              删除
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 处理卡片选择
  const handleCardSelect = (key: string, checked: boolean) => {
    setSelectedRowKeys((prev) => (checked ? [...prev, key] : prev.filter((k) => k !== key)));
  };

  // 处理编辑数据集
  const handleEditDataset = (values: any) => {
    if (currentDataset) {
      const updatedData = datasetData.map((item) => (item.key === currentDataset.key ? { ...item, ...values } : item));
      setDatasetData(updatedData);
      setEditModalVisible(false);
      setCurrentDataset(undefined);
    }
  };

  // 打开编辑弹窗
  const showEditModal = (record: DatasetItem) => {
    setCurrentDataset(record);
    setEditModalVisible(true);
  };

  // 打开详情抽屉
  const showDetailDrawer = (record: DatasetItem) => {
    setCurrentDataset(record);
    setDetailDrawerVisible(true);
  };

  // 渲染内容区域
  const renderContent = () => {
    if (viewType === 'list') {
      return (
        <Table
          rowKey="id"
          rowSelection={rowSelection}
          columns={columns}
          dataSource={datasetData}
          loading={loading}
          className="dataset-table"
          pagination={false}
          scroll={{ x: 'max-content', y: tableHeight }}
        />
      );
    }

    return (
      <div className="card-grid" style={{ height: selectedRowKeys.length > 0 ? 'calc(100vh - 270px)' : 'calc(100vh - 210px)' }}>
        <Row gutter={[20, 20]}>
          <Col xs={24} sm={12} md={8} key="add-card">
            <AddDatasetCard
              onAdd={() => {
                setModalVisible(true);
              }}
              selectedRowKeys={selectedRowKeys}
            />
          </Col>
          {datasetData.map((item) => (
            <Col xs={24} sm={12} md={8} key={item.key}>
              <DatasetCard
                item={item}
                onSelect={handleCardSelect}
                selected={selectedRowKeys.includes(item.key)}
                showEditModal={showEditModal}
                showDetailDrawer={showDetailDrawer}
              />
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  // 渲染页面
  return (
    <div className="dataset-management-container">
      {/* 页面标题和操作按钮 */}
      <div className="header-container">
        <Title level={5}>数据集列表</Title>
        {/* @ts-ignore */}
        <Segmented
          value={viewType}
          onChange={(value) => setViewType(value as 'list' | 'card')}
          options={[
            {
              value: 'list',
              icon: <Image src={image_1749694653385_2whmuf} preview={false} />,
            },
            {
              value: 'card',
              icon: <Image src={image_1749694664016_aaca5f} preview={false} />,
            },
          ]}
          className="view-type-segmented"
        />
      </div>

      {/* 搜索区域 */}
      <div className="search-area">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input
              placeholder="请输入名称、描述"
              suffix={<SearchOutlined />}
              allowClear
              value={pageInfo.search}
              onChange={(e) => setPageInfo({ ...pageInfo, search: e.target.value })}
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="请选择版本"
              style={{ width: '100%' }}
              allowClear
              value={pageInfo.version || undefined}
              onChange={(value) => setPageInfo({ ...pageInfo, version: value })}
            >
              <Select.Option value="2024">2024</Select.Option>
              <Select.Option value="latest">latest</Select.Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Space size={12}>
              <Button type="primary" icon={<SearchOutlined />} onClick={() => getDatasetListData()}>
                查询
              </Button>
              <Button
                onClick={() => {
                  setPageInfo({
                    page: 1,
                    page_size: 10,
                    total: 0,
                    search: '',
                    version: '',
                  });
                }}
              >
                重置
              </Button>
            </Space>
          </Col>
          <Col xs={24} sm={24} md={24} lg={6} style={{ textAlign: 'right' }}>
            <Space size={12}>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setModalVisible(true)}>
                添加数据集
              </Button>
              <Button type="text" icon={<PlusOutlined />} className="ant-gray">
                批量添加
              </Button>
              <Image src={image_1749699967101_1lulvx} style={{ cursor: 'pointer' }} preview={false} />
            </Space>
          </Col>
        </Row>
      </div>

      {/* 内容区域 */}
      <div className="table-area">
        {renderContent()}
        {/* 分页区域 */}
        {viewType === 'list' && (
          <div className="pagination-area">
            <Space className="pagination-total" size={4}>
              共计{datasetData.length}条
            </Space>
            <Pagination
              current={pageInfo.page}
              pageSize={pageInfo.page_size}
              total={pageInfo.total}
              showSizeChanger={true}
              showQuickJumper={true}
              pageSizeOptions={['10', '20', '50', '100']}
              onChange={(page, pageSize) => {
                setPageInfo((prev: any) => ({
                  ...prev,
                  page,
                  page_size: pageSize,
                }));
              }}
            />
          </div>
        )}
        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="batch-operation">
            <Checkbox
              style={{ marginRight: '20px' }}
              checked={selectedRowKeys.length === datasetData.length}
              indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < datasetData.length}
              onChange={(e) => {
                setSelectedRowKeys(e.target.checked ? datasetData.map((item) => item.key) : []);
              }}
            >
              全选
            </Checkbox>
            <Space style={{ marginRight: '60px' }} size={8}>
              已选 <span style={{ color: 'var(--ant-primary-color)' }}>{selectedRowKeys.length}</span> 条
            </Space>
            <Button type="primary" style={{ marginRight: '12px' }}>
              批量备份
            </Button>
            <Button danger>批量删除</Button>
          </div>
        )}
      </div>

      <AddDatasetModal
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          getDatasetListData();
        }}
        onOk={() => {
          setModalVisible(false);
          getDatasetListData();
        }}
      />
      <EditDatasetModal
        visible={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setCurrentDataset(undefined);
          getDatasetListData();
        }}
        onOk={handleEditDataset}
        dataset={currentDataset}
      />
      <DatasetDetailDrawer
        visible={detailDrawerVisible}
        onCancel={() => {
          setDetailDrawerVisible(false);
          setCurrentDataset(undefined);
        }}
        title={currentDataset?.chinese_name}
        content={currentDataset}
      />
    </div>
  );
});

export default DataManagement;
