/* 图像后处理页面样式 */
.image-processing {
  display: flex;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;

  &__main {
    flex: 1;
    overflow: hidden;
  }

  &__title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #333;
  }

  &__search {
    display: flex;
    margin-bottom: 15px;

    &-input {
      flex: 1;
      height: 40px;
      border-radius: 4px 0 0 4px;
    }

    &-button {
      height: 40px;
      border-radius: 0 4px 4px 0;
    }
  }

  &__hot-search {
    display: flex;
    align-items: center;
    margin-bottom: 25px;

    &-label {
      color: #666;
      margin-right: 10px;
    }

    &-tag {
      margin-right: 8px;
      cursor: pointer;
    }
  }

  &__stats {
    margin-bottom: 25px;

    &-text {
      font-size: 16px;
      color: #666;
    }

    &-number {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }
  }

  &__filters {
    display: flex;
    margin-bottom: 30px;
    flex-wrap: wrap;
  }

  &__filter-button {
    margin-right: 10px;
    margin-bottom: 10px;

    &--active {
      background-color: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }

    &--refresh {
      margin-left: auto;
    }
  }

  &__dropdown-menu {
    padding: 10px 0;
  }

  &__dropdown-item {
    padding: 8px 16px;
  }

  &__dropdown-checkbox {
    width: 100%;
  }

  &__card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  &__card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    height: 100%;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    &-image-container {
      height: 160px;
      overflow: hidden;
    }

    &-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &-content {
      padding: 16px;
    }

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    &-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      max-width: 80%;
    }

    &-tag {
      margin-left: 8px;
    }

    &-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &-stats {
      display: flex;
      align-items: center;
    }

    &-views,
    &-stars {
      display: flex;
      align-items: center;
      margin-right: 16px;
      color: #999;
      font-size: 14px;

      .anticon {
        margin-right: 4px;
      }
    }

    &-date {
      font-size: 12px;
      color: #999;
    }
  }
}

/* 响应式适配 */
@media screen and (max-width: 1200px) {
  .image-processing {
    &__card-list {
      grid-template-columns: repeat(auto-fill, minmax(250px, 2fr));
    }
  }
}

@media screen and (max-width: 768px) {
  .image-processing {
    &__card-list {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }
}
