import Axios, { AxiosResponse } from 'axios';
import { notification } from 'antd';
import cookies from 'js-cookie';
import { getI18n } from 'react-i18next';
import Cookies from 'js-cookie';
const baseApi = process.env.REACT_APP_BASE_URL || 'http://localhost/';

export type AxiosResFormat<T> = Promise<AxiosResponse<ResponseFormat<T>>>;
export interface ResponseFormat<T = any> {
  message: string;
  result: T;
  data: T;
  status: number;
}

// console.log(getI18n());

const axios = Axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? baseApi : '',
  timeout: 600000,
  responseType: 'json',
});

// 防重复处理401的标志位
let isHandling401 = false;

// 处理提示信息的函数
const showNotification = (content: string, type: 'success' | 'info' | 'warning' | 'error' = 'error') => {
  notification[type]({
    message: type === 'error' ? '错误' : type === 'success' ? '成功' : type === 'warning' ? '警告' : '提示',
    description: <div dangerouslySetInnerHTML={{ __html: content }}></div>,
  });
};

// 处理401登录超时的函数
const handle401Error = () => {
  if (isHandling401) {
    return; // 如果正在处理401，直接返回，避免重复处理
  }

  isHandling401 = true;
  showNotification('登录超时，需要重新登录', 'warning');

  // 延迟跳转，给用户时间看到提示
  setTimeout(() => {
    window.location.href = window.location.origin + '/frontend/login';
  }, 1000);
};

// 处理错误消息格式化
const formatErrorMessage = (data: any): string => {
  if (!data) return '未知错误';

  if (typeof data === 'string') {
    return data;
  }

  if (data.msg || data.message) {
    const message = data.msg || data.message;
    if (Object.prototype.toString.call(message) === '[object Object]') {
      return JSON.stringify(message);
    }
    return String(message);
  }

  return '未知错误';
};

// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    config.headers.set('language', getI18n().language);
    // 加入token
    const token = localStorage.getItem('myapp_token');
    if (token) {
      config.headers.set('Authorization', token);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    const { data } = response;

    if (data) {
      if (data.error_code === 0 || data.ret === 0) {
        return response;
      } else if (data.error_code && data.message) {
        const errMsg = formatErrorMessage(data);
        showNotification(errMsg, 'error');
        throw new Error(errMsg);
      } else {
        return response;
      }
    } else {
      return response;
    }
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    if (error.response) {
      const { data, status } = error.response;

      if (status === 401) {
        handle401Error();
      } else {
        const errMsg = formatErrorMessage(data);
        showNotification(errMsg, 'error');
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      showNotification('网络连接失败，请检查网络设置', 'error');
    } else {
      // 请求配置出错
      showNotification('请求配置错误', 'error');
    }

    return Promise.reject(error);
  },
);

export default axios;
