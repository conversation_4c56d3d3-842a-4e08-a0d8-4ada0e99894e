<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2319_2464)">
<path d="M10.2362 1.8929C10.7677 1.97815 11.1588 2.34621 11.4748 2.75879C11.8354 3.22975 12.2161 3.91893 12.6808 4.7526L12.9266 5.19206L13.231 5.71615C13.2953 5.81474 13.3335 5.85448 13.3742 5.88542C13.4518 5.94435 13.5533 5.98094 14.131 6.11165L14.6079 6.21908L15.84 6.50879C16.2072 6.60283 16.5302 6.69799 16.8019 6.80501C17.3578 7.02406 17.8602 7.35351 18.0536 7.97526C18.2448 8.59026 18.0284 9.15242 17.7069 9.66309C17.5483 9.91494 17.3418 10.189 17.0982 10.4899L16.2648 11.4779L15.9393 11.8587C15.5482 12.316 15.4779 12.4171 15.4445 12.5244C15.4105 12.6342 15.4116 12.7645 15.4706 13.3724L15.521 13.8802L15.6366 15.1904C15.6644 15.583 15.6776 15.9303 15.6642 16.2305C15.6375 16.8313 15.5008 17.4271 14.9872 17.8174C14.4629 18.2153 13.8551 18.1675 13.2863 18.0046C13.006 17.9242 12.6909 17.8026 12.3407 17.653L11.1818 17.1305L10.7359 16.9255C10.3279 16.7376 10.1688 16.6725 10.0767 16.652L10.0002 16.6439C9.95461 16.6439 9.9064 16.6518 9.80161 16.6911L9.2645 16.9255L8.81853 17.1305C7.97492 17.519 7.27483 17.8439 6.71404 18.0046C6.14527 18.1675 5.53745 18.2154 5.01319 17.8174C4.49961 17.4271 4.36287 16.8313 4.33611 16.2305C4.30939 15.6304 4.38622 14.8412 4.47934 13.8802L4.5298 13.3724L4.577 12.7604C4.58032 12.639 4.57277 12.5791 4.55584 12.5244C4.53914 12.4707 4.51259 12.4183 4.4419 12.3226L4.06105 11.8587L3.73552 11.4779C3.12031 10.7584 2.61063 10.1669 2.29347 9.66309C1.97203 9.15246 1.75558 8.5902 1.94679 7.97526C2.14015 7.35354 2.64263 7.02407 3.19842 6.80501C3.74242 6.59072 4.49167 6.42288 5.39243 6.21908L5.86931 6.11165L6.43572 5.97168C6.54253 5.93903 6.58741 5.9148 6.62615 5.88542C6.70765 5.82348 6.77687 5.72462 7.07374 5.19206L7.31951 4.7526L7.96241 3.61979C8.16094 3.28482 8.3453 2.99422 8.52556 2.75879C8.88651 2.28745 9.34548 1.87508 10.0002 1.875L10.2362 1.8929Z" fill="url(#paint0_linear_2319_2464)"/>
</g>
<defs>
<filter id="filter0_ii_2319_2464" x="1.875" y="1.875" width="16.2505" height="17.9164" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.66667"/>
<feGaussianBlur stdDeviation="2.75"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2319_2464"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.66667"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.586822 0 0 0 0 0.118269 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2319_2464" result="effect2_innerShadow_2319_2464"/>
</filter>
<linearGradient id="paint0_linear_2319_2464" x1="15.8333" y1="5" x2="5.83333" y2="18.3333" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB62F"/>
<stop offset="1" stop-color="#FF8D2F"/>
</linearGradient>
</defs>
</svg>
