.tablebox {
  padding: 16px 24px;
  background: #ffffff;
  width: 100%;

  .tablebox-title {
    font-size: 16px;
    font-weight: 600;
  }

  .tablebox-tag {
    display: inline-block;
    padding: 0 8px;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
  }

  .tablebox-finish-tag {
    background: #edf9f0;
    color: #23b541;
  }

  .tablebox-notfinish-tag {
    background: #dcefff;
    color: #0084f6;
  }
}

.react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 1;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}
