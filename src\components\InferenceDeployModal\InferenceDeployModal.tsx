import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, InputNumber, Button, message, Spin } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { getInferenceServiceModelViewInfo, createInferenceServiceModelView } from '../../api/kubeflowApi';
import './InferenceDeployModal.less';

const { Option } = Select;

interface InferenceDeployModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: (result: any) => void;
  modelData?: any;
}

interface DeployFormData {
  serviceType: string;
  projectGroup: string;
  label: string;
  memoryRequest: number;
  cpuRequest: number;
  gpuRequest: number;
  minReplicas: number;
  maxReplicas: number;
}

const InferenceDeployModal: React.FC<InferenceDeployModalProps> = ({ visible, onCancel, onSuccess, modelData }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [formConfig, setFormConfig] = useState<any>(null);
  const [configLoading, setConfigLoading] = useState(false);

  // 获取表单配置信息
  useEffect(() => {
    if (visible) {
      setConfigLoading(true);
      getInferenceServiceModelViewInfo()
        .then((res) => {
          if (res.status === 200 && res.data) {
            // 检查是否有 add_columns 数据
            if (res.data.add_columns && Array.isArray(res.data.add_columns) && res.data.add_columns.length > 0) {
              setFormConfig(res.data);

              // 从 add_columns 中提取默认值
              const defaultValues: any = {};
              res.data.add_columns.forEach((column: any) => {
                if (column.default !== undefined && column.default !== '') {
                  defaultValues[column.name] = column.default;
                }
              });

              // 设置表单默认值
              form.setFieldsValue({
                serviceType: defaultValues.service_type || '',
                projectGroup: defaultValues.project || '',
                label: defaultValues.label || 'xx模型，%s框架，xx版',
                memoryRequest: parseInt(defaultValues.resource_memory) || 5,
                cpuRequest: parseInt(defaultValues.resource_cpu) || 5,
                gpuRequest: parseInt(defaultValues.resource_gpu) || 0,
                minReplicas: defaultValues.min_replicas || 1,
                maxReplicas: defaultValues.max_replicas || 1,
              });
            } else {
              console.warn('API 返回的数据中没有 add_columns 或为空');
              message.warning('获取表单配置数据不完整，将使用默认值');
              setDefaultFormValues();
            }
          } else {
            console.warn('API 返回状态码不是 200 或没有数据');
            message.warning('获取表单配置失败，将使用默认值');
            setDefaultFormValues();
          }
        })
        .catch((error) => {
          console.error('获取表单配置失败:', error);
          message.error('获取表单配置出错，将使用默认值');
          setDefaultFormValues();
        })
        .finally(() => {
          setConfigLoading(false);
        });
    }
  }, [visible, form]);

  // 设置默认表单值的辅助函数
  const setDefaultFormValues = () => {
    form.setFieldsValue({
      serviceType: 'serving',
      projectGroup: '',
      label: 'xx模型，%s框架，xx版',
      memoryRequest: 5,
      cpuRequest: 5,
      gpuRequest: 0,
      minReplicas: 1,
      maxReplicas: 1,
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建保存数据，使用接口需要的字段名
      const saveData = {
        service_type: values.serviceType,
        project: values.projectGroup,
        label: values.label,
        model_name: modelData?.name || 'model',
        model_version: modelData?.version || 'v1.0.0',
        images: modelData?.images || 'ccr.ccs.tencentyun.com/cube-studio/tfserving:2.14.1-gpu',
        resource_memory: `${values.memoryRequest}G`,
        resource_cpu: values.cpuRequest.toString(),
        resource_gpu: values.gpuRequest.toString(),
        min_replicas: values.minReplicas,
        max_replicas: values.maxReplicas,
        priority: 1,
        ports: '80',
      };

      // 使用创建接口，因为这是新建推理部署
      const response = await createInferenceServiceModelView(saveData);

      if (response.data?.status === 0 || response.status === 200) {
        message.success('推理部署创建成功！');
        onSuccess?.(response.data);
        onCancel();
        form.resetFields();
      } else {
        message.error(response.data?.message || '创建失败，请重试');
      }
    } catch (error: any) {
      console.error('创建失败:', error);
      message.error(error.message || '创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="新建推理部署"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      className="inference-deploy-modal common-modal"
      destroyOnClose
    >
      <Spin spinning={loading || configLoading}>
        <Form form={form} layout="horizontal" className="inference-deploy-form" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          {/* 模型设置 */}
          <div className="form-section">
            <h3 className="section-title">模型设置</h3>

            <Form.Item label="服务类型" name="serviceType" rules={[{ required: true, message: '请选择服务类型' }]}>
              <Select placeholder="请选择服务类型">
                {formConfig?.add_columns
                  ?.find((col: any) => col.name === 'service_type')
                  ?.values?.map((option: any) => (
                    <Option key={option.id} value={option.value}>
                      {option.value}
                    </Option>
                  ))}
              </Select>
            </Form.Item>

            <Form.Item label="项目组" name="projectGroup" rules={[{ required: true, message: '请选择项目组' }]}>
              <Select placeholder="请选择项目组">
                {formConfig?.add_columns
                  ?.find((col: any) => col.name === 'project')
                  ?.values?.map((option: any) => (
                    <Option key={option.id} value={option.id}>
                      {option.value}
                    </Option>
                  ))}
              </Select>
            </Form.Item>

            <Form.Item label="标签" name="label" rules={[{ required: true, message: '请输入标签' }]}>
              <Input placeholder="请输入标签" />
            </Form.Item>
          </div>

          {/* 部署推理设置 */}
          <div className="form-section">
            <h3 className="section-title">部署推理设置</h3>

            <Form.Item label="内存申请" name="memoryRequest" rules={[{ required: true, message: '请输入内存申请量' }]}>
              <InputNumber min={1} max={64} placeholder="请输入内存申请量" addonAfter="G" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="CPU申请" name="cpuRequest" rules={[{ required: true, message: '请输入CPU申请量' }]}>
              <InputNumber min={1} max={32} placeholder="请输入CPU申请量" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="GPU申请" name="gpuRequest" rules={[{ required: true, message: '请输入GPU申请量' }]}>
              <InputNumber min={0} max={8} placeholder="请输入GPU申请量" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="副本数" className="replica-form-item">
              <div className="replica-row">
                <Form.Item name="minReplicas" rules={[{ required: true, message: '请输入最小副本数' }]} className="replica-item">
                  <div className="replica-input-group">
                    <span className="replica-label">Min</span>
                    <InputNumber min={1} max={10} placeholder="1" style={{ width: '100%' }} />
                  </div>
                </Form.Item>

                <span className="replica-separator">-</span>

                <Form.Item name="maxReplicas" rules={[{ required: true, message: '请输入最大副本数' }]} className="replica-item">
                  <div className="replica-input-group">
                    <span className="replica-label">Max</span>
                    <InputNumber min={1} max={10} placeholder="1" style={{ width: '100%' }} />
                  </div>
                </Form.Item>
              </div>
            </Form.Item>
          </div>

          {/* 底部按钮 */}
          <div className="form-actions">
            <Button onClick={handleCancel} className="cancel-btn">
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} className="submit-btn">
              开始推理
            </Button>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default InferenceDeployModal;
