import React, { useState, useEffect } from 'react';
import { Table, Button, Input, Select, Space, Tag, Dropdown, message, Modal, Row, Col, Pagination, Checkbox } from 'antd';
import { SearchOutlined, PlusOutlined, MoreOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { MenuProps } from 'antd';
import moment from 'moment';
import {
  getInferenceServiceList,
  deleteInferenceServiceModelView,
  getInferenceServiceModelViewDetail,
  getInferenceServiceModelViewInfo,
  copyInferenceServiceModelView,
} from '../../../api/kubeflowApi';
import { debugInferenceServiceWithXHR, deployInferenceServiceWithXHR, clearInferenceServiceWithXHR } from '../../../api/xhrRedirect';
import './InferenceService.less';
import InferenceDeployModal from '../../../components/InferenceDeployModal/InferenceDeployModal';
import InferenceServiceDetail from '../../../components/InferenceServiceDetail/InferenceServiceDetail';
import InferenceServiceEdit from '../../../components/InferenceServiceEdit/InferenceServiceEdit';
import image_1752802439074_y5pme4 from '../../../images/image_1752802439074_y5pme4.svg';

const { confirm } = Modal;

// 推理服务数据接口定义
interface InferenceServiceData {
  id: string;
  project: any; // 项目对象，包含name等字段
  label: string;
  model_name: string;
  model_name_url: string; // 模型名称的HTML链接
  model_version: string;
  created_on: string;
  changed_on: string;
  creator: string; // 创建者
  modified: string; // 修改时间
  min_replicas: number;
  max_replicas: number;
  replicas_html: string; // 副本数的HTML显示
  status: string;
  status_url: string; // 状态的HTML链接
  service_type: string;
  resource_memory: string;
  resource_cpu: string;
  resource_gpu: string;
  resource: string; // 资源信息
  images: string;
  ports: string;
  priority: number;
  inference_host_url: string; // 网络信息的HTML链接
  ip: string; // IP地址
  operate_html: string; // 操作的HTML链接
}

// 表格高度自适应 hook
const useTableHeight = () => {
  const [tableHeight, setTableHeight] = useState<number>(0);

  useEffect(() => {
    const calculateHeight = () => {
      const headerHeight = document.querySelector('.header-container')?.clientHeight || 0;
      const searchAreaHeight = document.querySelector('.search-area')?.clientHeight || 0;
      const paginationHeight = document.querySelector('.pagination-area')?.clientHeight || 0;
      const margin = 120; // 上下边距
      const height = window.innerHeight - headerHeight - searchAreaHeight - paginationHeight - margin;
      setTableHeight(Math.max(height, 200)); // 最小高度 200px
    };
    calculateHeight();
    window.addEventListener('resize', calculateHeight);
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);
  return tableHeight;
};

const InferenceService: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [debugLoading, setDebugLoading] = useState<Record<number, boolean>>({});
  const [dataSource, setDataSource] = useState<InferenceServiceData[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [projectFilter, setProjectFilter] = useState<number | undefined>(undefined);
  const [projectOptions, setProjectOptions] = useState<any[]>([]);

  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `共 ${total} 条记录，当前显示 ${range[0]}-${range[1]} 条`,
  });

  // 多选相关
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  const tableHeight = useTableHeight();

  // 获取项目组选项
  const fetchProjectOptions = async () => {
    try {
      const response = await getInferenceServiceModelViewInfo();
      if (response.status === 200 && response.data) {
        const { add_columns } = response.data;

        // 获取项目组选项，但不设置默认值
        if (add_columns && Array.isArray(add_columns)) {
          const projectColumn = add_columns.find((col: any) => col.name === 'project');
          if (projectColumn && projectColumn.values) {
            setProjectOptions(projectColumn.values);
          }
        }
      }
    } catch (error) {
      console.error('获取项目组选项失败:', error);
    }
  };

  // 构建搜索过滤器
  const buildFilters = () => {
    const filters = [];

    // 名称搜索过滤器
    if (searchValue) {
      filters.push({
        col: 'model_name',
        opr: 'ct',
        value: searchValue,
      });
    }

    // 项目过滤器
    if (projectFilter) {
      filters.push({
        col: 'project',
        opr: 'rel_o_m',
        value: projectFilter, // 使用用户输入的项目值
      });
    }

    // 状态过滤器
    if (statusFilter) {
      filters.push({
        col: 'model_status',
        opr: 'ct',
        value: statusFilter,
      });
    }

    return filters;
  };

  // 获取推理服务列表
  const fetchInferenceServiceList = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const filters = buildFilters();

      const form_data = JSON.stringify({
        filters,
        str_related: 1,
        page: page - 1, // 后端页码从0开始
        page_size: pageSize,
      });

      const response = await getInferenceServiceList({ form_data });
      if (response.status === 200 && response.data) {
        const { result } = response.data;
        if (result && result.data) {
          setDataSource(result.data);
          setPagination((prev) => ({
            ...prev,
            current: page,
            pageSize,
            total: result.count || 0,
          }));
        }
      }
    } catch (error) {
      console.error('获取推理服务列表失败:', error);
      message.error('获取推理服务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理函数
  const handleSearch = () => {
    fetchInferenceServiceList(1, pagination.pageSize);
  };

  // 重置搜索条件
  const handleReset = () => {
    setSearchValue('');
    setStatusFilter('');
    setProjectFilter(undefined);
    // 重置后立即搜索
    setTimeout(() => {
      fetchInferenceServiceList(1, pagination.pageSize);
    }, 0);
  };

  // 初始化加载数据
  useEffect(() => {
    fetchInferenceServiceList();
    fetchProjectOptions();
  }, []);

  // 服务资源监控
  const handleServiceResourceMonitor = () => {
    try {
      // 跳转到 Grafana 监控页面
      const monitorUrl = `${process.env.REACT_APP_BASE_URL}/grafana/d/istio-service/istio-service?var-namespace=service&var-service=All`;
      window.open(monitorUrl, '_blank');
    } catch (error) {
      console.error('服务资源监控跳转失败:', error);
      message.error('服务资源监控跳转失败');
    }
  };

  // 调试操作
  const handleDebug = async (record: InferenceServiceData) => {
    setDebugLoading((prev) => ({ ...prev, [record.id]: true }));
    try {
      // 使用XHR方法处理调试302重定向
      const result = await debugInferenceServiceWithXHR(record.id);
      if (result.success) {
        fetchInferenceServiceList(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error('调试操作失败:', error);
      message.error('调试操作失败');
    } finally {
      setDebugLoading((prev) => ({ ...prev, [record.id]: false }));
    }
  };

  // 部署操作
  const handleDeploy = async (record: InferenceServiceData) => {
    try {
      // 使用XHR方法处理部署302重定向，传入完整的endpoint
      const result = await deployInferenceServiceWithXHR(record.id);
      if (result.success) {
        fetchInferenceServiceList(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error('部署操作失败:', error);
      message.error('部署操作失败');
    }
  };

  // 监控操作
  const handleMonitor = async (record: InferenceServiceData) => {
    try {
      // 将 operate_html 转换为对象并提取监控的 href
      if (record.operate_html) {
        // 创建一个临时的 DOM 元素来解析 HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = record.operate_html;

        // 查找包含"监控"文本的链接
        const monitorLink = Array.from(tempDiv.querySelectorAll('a')).find(
          (link) => link.textContent?.includes('监控') || link.title?.includes('监控'),
        );

        if (monitorLink && monitorLink.getAttribute('href')) {
          window.open(monitorLink.href, '_blank');
          return;
        }
      }
    } catch (error) {
      console.error('监控跳转失败:', error);
      message.error('监控跳转失败');
    }
  };

  // 清理操作
  const handleClear = async (record: InferenceServiceData) => {
    try {
      // 使用XHR方法处理部署302重定向，传入完整的endpoint
      const result = await clearInferenceServiceWithXHR(record.id);
      if (result.success) {
        fetchInferenceServiceList(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error('清理操作失败:', error);
      message.error('清理操作失败');
    }
  };

  // 删除操作
  const handleDelete = (record: InferenceServiceData) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除推理服务 "${record.label}" 吗？此操作不可恢复。`,
      onOk: async () => {
        try {
          const response = await deleteInferenceServiceModelView(record.id);
          if (response.status === 200) {
            message.success('删除成功');
            fetchInferenceServiceList(pagination.current, pagination.pageSize);
          } else {
            message.error(response.data?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 批量复制操作
  const handleBatchCopy = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要复制的推理服务');
      return;
    }

    confirm({
      title: '确认批量复制',
      icon: <ExclamationCircleOutlined />,
      content: `确定要复制选中的 ${selectedRowKeys.length} 个推理服务吗？`,
      onOk: async () => {
        try {
          const response = await copyInferenceServiceModelView({
            ids: selectedRowKeys,
          });

          if (response.status === 200) {
            message.success(`成功复制 ${selectedRowKeys.length} 个推理服务`);
            setSelectedRowKeys([]); // 清空选择
            fetchInferenceServiceList(pagination.current, pagination.pageSize);
          } else {
            message.error(response.data?.message || '批量复制失败');
          }
        } catch (error) {
          console.error('批量复制失败:', error);
          message.error('批量复制失败');
        }
      },
    });
  };

  // 批量删除操作
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的推理服务');
      return;
    }

    confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 个推理服务吗？此操作不可恢复。`,
      onOk: async () => {
        try {
          // 逐个删除选中的服务
          const deletePromises = selectedRowKeys.map((id) => deleteInferenceServiceModelView(id as string));

          const results = await Promise.allSettled(deletePromises);

          // 统计成功和失败的数量
          const successCount = results.filter((result) => result.status === 'fulfilled' && result.value.status === 200).length;
          const failCount = selectedRowKeys.length - successCount;

          if (successCount > 0) {
            message.success(`成功删除 ${successCount} 个推理服务${failCount > 0 ? `，${failCount} 个删除失败` : ''}`);
          } else {
            message.error('批量删除失败');
          }

          setSelectedRowKeys([]); // 清空选择
          fetchInferenceServiceList(pagination.current, pagination.pageSize);
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      },
    });
  };

  // 查看详情
  const handleViewDetail = (record: InferenceServiceData) => {
    setCurrentServiceId(record.id);
    setDetailModalVisible(true);
  };

  // 修改操作
  const handleEdit = (record: InferenceServiceData) => {
    setCurrentServiceId(record.id);
    setEditModalVisible(true);
  };

  // 操作菜单
  const getActionMenu = (record: InferenceServiceData): MenuProps['items'] => [
    {
      key: 'detail',
      label: '详情',
      onClick: () => handleViewDetail(record),
    },
    {
      key: 'edit',
      label: '修改',
      onClick: () => handleEdit(record),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
      onClick: () => handleDelete(record),
    },
  ];

  // 表格列定义
  const columns: ColumnsType<any> = [
    {
      title: '项目组',
      dataIndex: 'project',
      key: 'project',
      width: 150,
      render: (project) => {
        // 处理项目显示，支持对象和字符串两种格式
        if (project && typeof project === 'object') {
          return <span>{project.name || project.label || ''}</span>;
        }
        return <span>{project || ''}</span>;
      },
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      width: 150,
      render: (text) => <span style={{ whiteSpace: 'nowrap' }}>{text || ''}</span>,
    },
    {
      title: '模型',
      dataIndex: 'model_name_url',
      key: 'model_name_url',
      width: 180,
      render: (html, record) => (
        <div className="compact-cell">
          {html ? <div dangerouslySetInnerHTML={{ __html: html }} /> : <div>{record.model_name || ''}</div>}
          <div className="compact-cell-content">
            <span className="compact-cell-label">类型</span>
            <span className="compact-cell-value">{record.service_type || 'tfserving'}</span>
          </div>
        </div>
      ),
    },
    {
      title: '网络信息',
      dataIndex: 'inference_host_url',
      key: 'inference_host_url',
      width: 200,
      render: (html, record) => (
        <div className="compact-cell">
          <div className="compact-cell-content">
            <span className="compact-cell-label">域名</span>
            <span className="compact-cell-value">{html ? <div dangerouslySetInnerHTML={{ __html: html }} /> : <div>-</div>}</span>
          </div>
          <div className="compact-cell-content">
            <span className="compact-cell-label">IP</span>
            <span className="compact-cell-value">{record.ip ? <div dangerouslySetInnerHTML={{ __html: record.ip }} /> : '-'}</span>
          </div>
        </div>
      ),
    },
    {
      title: '版本',
      dataIndex: 'model_version',
      key: 'model_version',
      width: 120,
      render: (text, record) => (
        <div className="compact-cell">
          <div className="compact-cell-content">
            <span className="compact-cell-label">版本</span>
            <span className="compact-cell-value">{text || '-'}</span>
          </div>
          <div className="compact-cell-content">
            <span className="compact-cell-label">
              <img src={image_1752802439074_y5pme4} alt="操作员" />
              {record.creator || 'admin'} · {record.modified || record.changed_on || '-'}
            </span>
          </div>
        </div>
      ),
    },
    {
      title: '部署资源',
      dataIndex: 'resource',
      key: 'resource',
      width: 160,
      render: (_, record) => (
        <div className="compact-cell">
          <div className="compact-cell-content">
            <span className="compact-cell-label">副本</span>
            <span className="compact-cell-value">
              {record.replicas_html ? (
                <span dangerouslySetInnerHTML={{ __html: record.replicas_html }} />
              ) : (
                <span>
                  {record.min_replicas || 1}-{record.max_replicas || 1}
                </span>
              )}
            </span>
          </div>
          <div className="compact-cell-content">
            <span className="compact-cell-label">资源</span>
            <span className="compact-cell-value">
              {record.resource ||
                `cpu:${record.resource_cpu || '0.5'},memory:${record.resource_memory || '5G'},gpu:${record.resource_gpu || '0'}`}
            </span>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status_url',
      key: 'status_url',
      width: 100,
      render: (statusUrl, record) => (
        <div className="compact-cell">
          {statusUrl ? <div dangerouslySetInnerHTML={{ __html: statusUrl }} /> : <div>{record.status || ''}</div>}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'operations',
      width: 200,
      render: (_, record) => (
        <Space size={12}>
          <Button
            type="link"
            size="small"
            className="operation-button"
            loading={debugLoading[record.id]}
            onClick={() => handleDebug(record)}
          >
            调试
          </Button>
          <Button type="link" size="small" className="operation-button" onClick={() => handleDeploy(record)}>
            部署
          </Button>
          <Button type="link" size="small" className="operation-button" onClick={() => handleMonitor(record)}>
            监控
          </Button>
          <Button type="link" size="small" className="operation-button" onClick={() => handleClear(record)}>
            清理
          </Button>
          <Dropdown
            menu={{
              items: getActionMenu(record),
            }}
            trigger={['click']}
            placement="bottomLeft"
          >
            <Button type="link" size="small" className="operation-button more-button">
              <MoreOutlined />
            </Button>
          </Dropdown>
        </Space>
      ),
    },
  ];

  // 表格变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    fetchInferenceServiceList(paginationConfig.current, paginationConfig.pageSize);
  };

  const [deployModalVisible, setDeployModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentServiceId, setCurrentServiceId] = useState<string | null>(null);

  return (
    <div className="inference-service-page">
      <div className="header-container">
        <h5>推理服务列表</h5>
      </div>

      {/* 搜索和筛选区域 */}
      <Row gutter={16} align="middle" style={{ marginBottom: 24 }} className="search-area">
        <Col flex="auto">
          <Row gutter={16}>
            <Col span={5}>
              <Input
                placeholder="请输入模型名称"
                allowClear
                style={{ width: '100%' }}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onPressEnter={handleSearch}
              />
            </Col>
            <Col span={5}>
              <Select
                placeholder="项目组"
                allowClear
                style={{ width: '100%' }}
                value={projectFilter}
                onChange={(value: number | undefined) => setProjectFilter(value)}
              >
                {projectOptions.map((option: any) => (
                  <Select.Option key={option.id} value={option.id}>
                    {option.value}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={5}>
              <Input
                placeholder="状态"
                allowClear
                style={{ width: '100%' }}
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                onPressEnter={handleSearch}
              />
            </Col>
            <Col span={4}>
              <Space size={8}>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </Col>
        <Col flex="none" style={{ textAlign: 'right' }}>
          <Space size={12}>
            <Button onClick={handleServiceResourceMonitor}>服务资源监控</Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setDeployModalVisible(true)}>
              添加推理服务
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 表格区域 */}
      <div className="table-area">
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
          onChange={handleTableChange}
          rowKey="id"
          scroll={{ x: 1200, y: tableHeight }}
          size="middle"
        />
        {/* 分页区域 */}
        <div className="pagination-area">
          <Space className="pagination-total" size={4}>
            共计{pagination.total}条
          </Space>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            showSizeChanger={true}
            showQuickJumper={true}
            pageSizeOptions={['10', '20', '50', '100']}
            onChange={(page, pageSize) => {
              setPagination((prev) => ({
                ...prev,
                current: page,
                pageSize: pageSize,
              }));
              fetchInferenceServiceList(page, pageSize);
            }}
          />
        </div>
        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="batch-operation">
            <Checkbox
              style={{ marginRight: '20px' }}
              checked={selectedRowKeys.length === dataSource.length && dataSource.length > 0}
              indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < dataSource.length}
              onChange={(e) => {
                setSelectedRowKeys(e.target.checked ? dataSource.map((item) => item.id) : []);
              }}
            >
              全选
            </Checkbox>
            <Space style={{ marginRight: '60px' }} size={8}>
              已选 <span style={{ color: 'var(--ant-primary-color)' }}>{selectedRowKeys.length}</span> 条
            </Space>
            <Button type="primary" style={{ marginRight: '12px' }} onClick={handleBatchCopy}>
              批量复制
            </Button>
            <Button danger onClick={handleBatchDelete}>
              批量删除
            </Button>
          </div>
        )}
      </div>
      <InferenceDeployModal
        visible={deployModalVisible}
        onCancel={() => setDeployModalVisible(false)}
        onSuccess={() => {
          setDeployModalVisible(false);
          fetchInferenceServiceList(1, pagination.pageSize);
        }}
      />

      {/* 详情弹窗 */}
      <InferenceServiceDetail
        visible={detailModalVisible}
        serviceId={currentServiceId}
        onCancel={() => {
          setDetailModalVisible(false);
          setCurrentServiceId(null);
        }}
      />

      {/* 编辑弹窗 */}
      <InferenceServiceEdit
        visible={editModalVisible}
        serviceId={currentServiceId}
        onCancel={() => {
          setEditModalVisible(false);
          setCurrentServiceId(null);
        }}
        onSuccess={() => {
          setEditModalVisible(false);
          setCurrentServiceId(null);
          fetchInferenceServiceList(pagination.current, pagination.pageSize);
        }}
      />
    </div>
  );
};

export default InferenceService;
