import React, { useState, useEffect } from 'react';
import { Drawer, Descriptions, Tag, Spin, message, Button, Space, Divider } from 'antd';
import { getInferenceServiceModelViewDetail } from '../../api/kubeflowApi';
import './InferenceServiceDetail.less';

interface InferenceServiceDetailProps {
  visible: boolean;
  serviceId: string | null;
  onCancel: () => void;
}

interface ServiceDetailData {
  id: string;
  label: string;
  project: any;
  model_name: string;
  model_version: string;
  service_type: string;
  model_status: string;
  created_on: string;
  changed_on: string;
  min_replicas: number;
  max_replicas: number;
  resource_memory: string;
  resource_cpu: string;
  resource_gpu: string;
  images: string;
  ports: string;
  inference_host_url: string;
  priority: number;
  description?: string;
}

// 状态标签颜色映射
const statusColorMap: Record<string, string> = {
  已上线: 'success',
  运行中: 'processing',
  已停止: 'error',
  部署中: 'warning',
  失败: 'error',
  等待中: 'default',
};

const InferenceServiceDetail: React.FC<InferenceServiceDetailProps> = ({ visible, serviceId, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState<ServiceDetailData | null>(null);

  // 获取详情数据
  const fetchDetailData = async () => {
    if (!serviceId) return;

    setLoading(true);
    try {
      const response = await getInferenceServiceModelViewDetail(serviceId, {});
      if (response.status === 200 && response.data) {
        setDetailData(response.data.result || response.data);
      } else {
        message.error('获取详情失败');
      }
    } catch (error: any) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && serviceId) {
      fetchDetailData();
    }
  }, [visible, serviceId]);

  const handleCancel = () => {
    setDetailData(null);
    onCancel();
  };

  return (
    <Drawer
      title="推理服务详情"
      open={visible}
      onClose={handleCancel}
      width={800}
      className="inference-service-detail-drawer"
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={handleCancel}>关闭</Button>
        </div>
      }
    >
      <Spin spinning={loading}>
        {detailData ? (
          <div className="detail-content">
            {/* 基本信息 */}
            <Descriptions title="基本信息" bordered column={2} size="small">
              <Descriptions.Item label="服务标签" span={2}>
                <strong>{detailData.label}</strong>
              </Descriptions.Item>
              <Descriptions.Item label="项目组">
                {detailData.project && typeof detailData.project === 'object'
                  ? detailData.project.name || detailData.project.label
                  : detailData.project || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusColorMap[detailData.model_status] || 'default'}>{detailData.model_status}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="优先级">{detailData.priority || 0}</Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {detailData.project.created_on || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="修改时间" span={2}>
                {detailData.project.changed_on || '-'}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            {/* 模型信息 */}
            <Descriptions title="模型信息" bordered column={2} size="small">
              <Descriptions.Item label="模型名称">{detailData.model_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="模型版本">{detailData.model_version || '-'}</Descriptions.Item>
              <Descriptions.Item label="服务类型" span={2}>
                {detailData.service_type || 'tfserving'}
              </Descriptions.Item>
              <Descriptions.Item label="镜像" span={2}>
                {detailData.images || '-'}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            {/* 部署配置 */}
            <Descriptions title="部署配置" bordered column={2} size="small">
              <Descriptions.Item label="最小副本数">{detailData.min_replicas || 1}</Descriptions.Item>
              <Descriptions.Item label="最大副本数">{detailData.max_replicas || 1}</Descriptions.Item>
              <Descriptions.Item label="CPU资源">{detailData.resource_cpu || '-'}</Descriptions.Item>
              <Descriptions.Item label="内存资源">{detailData.resource_memory || '-'}</Descriptions.Item>
              <Descriptions.Item label="GPU资源">{detailData.resource_gpu || '0'}</Descriptions.Item>
              <Descriptions.Item label="端口配置">{detailData.ports || '-'}</Descriptions.Item>
            </Descriptions>

            <Divider />

            {/* 网络信息 */}
            <Descriptions title="网络信息" bordered column={2} size="small">
              <Descriptions.Item label="访问地址" span={2}>
                {detailData.inference_host_url ? <div dangerouslySetInnerHTML={{ __html: detailData.inference_host_url }} /> : '-'}
              </Descriptions.Item>
            </Descriptions>

            {detailData.description && (
              <>
                <Divider />
                <Descriptions title="描述信息" bordered column={1} size="small">
                  <Descriptions.Item label="描述">{detailData.description}</Descriptions.Item>
                </Descriptions>
              </>
            )}
          </div>
        ) : (
          !loading && <div style={{ textAlign: 'center', padding: '40px 0' }}>暂无数据</div>
        )}
      </Spin>
    </Drawer>
  );
};

export default InferenceServiceDetail;
