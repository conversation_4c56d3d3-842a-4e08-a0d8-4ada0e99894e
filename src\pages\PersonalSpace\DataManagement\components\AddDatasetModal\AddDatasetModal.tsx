import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Input, Button, Select, Upload, Form, message, Space, Image } from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import image_1749718147814_89muqh from '../../../../../images/image_1749718147814_89muqh.svg';
import { getTagList, createDataset, uploadFile } from '../../../../../api/kubeflowApi';

// 引入样式文件
import './AddDatasetModal.less';

const { TextArea } = Input;
const { Option } = Select;

interface DatasetModalProps {
  /**
   * 弹窗是否可见
   */
  visible: boolean;
  /**
   * 弹窗标题
   */
  title?: string;
  /**
   * 确认按钮文本
   */
  okText?: string;
  /**
   * 取消按钮文本
   */
  cancelText?: string;
  /**
   * 点击确认按钮时的回调
   * @param data 表单数据
   */
  onOk?: (data: FormData) => void;
  /**
   * 点击取消按钮时的回调
   */
  onCancel?: () => void;
}

interface FormData {
  name: string;
  chinese_name: string;
  description: string;
  tags: string;
  version: string;
  path: string;
  icon: string;
  features: string;
}

const DatasetModal: React.FC<DatasetModalProps> = ({
  visible,
  title = '添加数据集',
  okText = '确定',
  cancelText = '取消',
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);
  const [path, setPath] = useState<string>('');
  // 标签数据
  const [tags, setTags] = useState<any[]>([]);
  // 版本数据
  const [versionData, setVersionData] = useState<any[]>([]);

  // 获取tag列表
  useEffect(() => {
    if (visible) {
      getTagList().then((res) => {
        const { status, data } = res;
        if (status === 200) {
          setTags(data.result.data);
        }
      });
    }
  }, [visible]);

  // 重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      setFileList([]);
      setPath('');
    }
  }, [visible, form]);

  // 处理文件上传（自定义上传）
  const handleCustomRequest = useCallback((options: any) => {
    const { file, onSuccess, onError } = options;
    const formData = new FormData();
    formData.append('file', file);
    uploadFile(formData)
      .then((res) => {
        if (res.status === 200) {
          console.log(res);
          setPath(res.data.result.file_path);
          message.success('上传成功');
          onSuccess && onSuccess(res, file);
        } else {
          message.error('上传失败');
          onError && onError(new Error('上传失败'));
        }
      })
      .catch((err) => {
        message.error('上传异常');
        onError && onError(err);
      });
  }, []);

  // 文件上传前校验
  const beforeUpload = useCallback(
    (file: any) => {
      // 假设只允许上传一个文件
      if (fileList.length >= 1) {
        message.error('只能上传一个文件！');
        return Upload.LIST_IGNORE;
      }
      // 可以在这里添加文件类型、大小等校验
      return true;
    },
    [fileList],
  );

  // 处理文件列表变化
  const handleFileChange = useCallback(({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  }, []);

  // 确认按钮点击事件
  const handleOk = useCallback(async () => {
    try {
      const values = await form.validateFields();

      if (!path) {
        message.error('请上传文件！');
        return;
      }

      const formData: FormData = {
        name: values.name,
        chinese_name: values.chinese_name,
        description: values.description,
        tags: values.tags.join(','),
        version: values.version,
        path,
        icon: values.icon || '',
        features: values.features || '',
      };

      createDataset(formData).then((res) => {
        const { status, data } = res;
        if (status === 200) {
          message.success('创建成功');
          handleCancel();
        }
      });
    } catch (error) {
      console.log('表单验证失败:', error);
    }
  }, [form, path, onOk]);

  // 取消按钮点击事件
  const handleCancel = useCallback(() => {
    form.resetFields();
    setFileList([]);
    setPath('');
    onCancel?.();
  }, [form, onCancel]);

  return (
    <Modal
      open={visible}
      title={title}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          {cancelText}
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          {okText}
        </Button>,
      ]}
      width="50%" // 根据设计稿调整宽度
      className="dataset-modal common-modal" // 应用自定义样式
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        initialValues={{
          name: '',
          chinese_name: '',
          description: '',
          tags: [],
          version: '',
          icon: '',
          features: '',
        }}
      >
        <div className="dataset-modal__section dataset-modal__section--required-info">
          <h4 className="dataset-modal__subsection-title">必填信息</h4>
          <Form.Item name="name" label="名称" rules={[{ required: true, message: '请输入数据集名称' }]}>
            <Input placeholder="请输入数据集名称" className="dataset-modal__input" />
          </Form.Item>
          <Form.Item name="chinese_name" label="中文名" rules={[{ required: true, message: '请输入数据集中文名称' }]}>
            <Input placeholder="请输入数据集中文名称" className="dataset-modal__input" />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ required: true, message: '请输入数据集描述' }]}>
            <TextArea rows={4} placeholder="请输入数据集描述" className="dataset-modal__textarea" />
          </Form.Item>
          <Form.Item name="tags" label="标签" rules={[{ required: true, message: '请选择标签' }]}>
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              className="dataset-modal__select"
              placeholder="请选择标签"
              options={tags.map((tag) => ({ label: tag.name, value: tag.id }))}
            />
          </Form.Item>
          <Form.Item name="version" label="版本" rules={[{ required: true, message: '请选择版本' }]}>
            <Select placeholder="请选择版本" className="dataset-modal__select">
              <Option value="latest">latest</Option>
              <Option value="v1.0">v1.0</Option>
              <Option value="v2.0">v2.0</Option>
            </Select>
          </Form.Item>
          <Form.Item label="添加文件">
            <Upload
              customRequest={handleCustomRequest}
              fileList={fileList}
              beforeUpload={beforeUpload}
              onChange={handleFileChange}
              maxCount={1}
              className="dataset-modal__upload"
            >
              <Button style={{ width: '256px' }}>
                <Space size={4}>
                  <Image src={image_1749718147814_89muqh} preview={false} />
                  <span style={{ fontSize: '16px', color: 'rgba(0, 0, 0, 0.85)', letterSpacing: 'normal' }}>上传</span>
                </Space>
              </Button>
            </Upload>
          </Form.Item>
        </div>

        <div className="dataset-modal__section dataset-modal__section--more-settings">
          <h4 className="dataset-modal__subsection-title">设置更多</h4>
          <Form.Item name="icon" label="图标">
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Form.Item name="icon" noStyle>
                <Input
                  placeholder="请输入图片地址、svg源码、或帮助文档链接"
                  className="dataset-modal__input dataset-modal__input--preview-image"
                  style={{ flex: 1 }}
                />
              </Form.Item>
              <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.icon !== currentValues.icon}>
                {({ getFieldValue }) => {
                  const iconValue = getFieldValue('icon');
                  return iconValue ? (
                    <div className="dataset-modal__preview-image-wrapper">
                      <Image
                        src={iconValue}
                        alt="图标"
                        className="dataset-modal__preview-image"
                        style={{ width: '48px', height: '48px' }}
                      />
                    </div>
                  ) : (
                    <div className="dataset-modal__image-placeholder"></div>
                  );
                }}
              </Form.Item>
            </div>
          </Form.Item>
          <Form.Item name="features" label="特征">
            <TextArea
              rows={4}
              placeholder="（非必填）数据集中的列信息，要求数据集中要有data.csv文件，用于表示数据集中的全部数据"
              className="dataset-modal__textarea"
            />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};

export default DatasetModal;
